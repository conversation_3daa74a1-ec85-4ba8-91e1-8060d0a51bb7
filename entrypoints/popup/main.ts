import './style.css';

// 发送消息到后台脚本
const sendMessage = async (action: string): Promise<void> => {
  try {
    await browser.runtime.sendMessage({ action });
    
    setTimeout(() => {
      window.close();
    }, 500);
    
  } catch (error) {
    console.error('导出失败:', error);
  }
};

// 导出处理函数
const handleExportPdf = () => sendMessage("exportPdf");
const handleExportWord = () => sendMessage("exportWord");
const handleExportMd = () => sendMessage("exportMd");

// 绑定事件监听器
const bindEventListeners = (): void => {
  document.getElementById('exportPdf')?.addEventListener('click', handleExportPdf);
  document.getElementById('exportWord')?.addEventListener('click', handleExportWord);
  document.getElementById('exportMd')?.addEventListener('click', handleExportMd);
};

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  bindEventListeners();
});