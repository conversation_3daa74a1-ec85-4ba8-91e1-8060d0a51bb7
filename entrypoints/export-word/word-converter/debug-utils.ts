import { PageBlock, Blocks } from '@/pkg/lark/docx'

/**
 * 调试工具：分析图片块的详细信息
 */
export function debugImageBlocks(rootBlock: PageBlock): void {
  console.group('🔍 === 图片块调试信息 ===')
  
  const imageBlocks = collectImageBlocks(rootBlock)
  console.log(`📊 总共发现 ${imageBlocks.length} 个图片块`)
  
  if (imageBlocks.length === 0) {
    console.log('ℹ️ 没有发现图片块')
    console.groupEnd()
    return
  }

  imageBlocks.forEach((block, index) => {
    console.group(`🖼️ 图片块 ${index + 1}`)
    
    // 基本信息
    console.log('📋 基本信息:', {
      id: block.id,
      type: block.type,
      hasSnapshot: !!block.snapshot,
      hasZoneState: !!block.zoneState,
    })
    
    // Snapshot 信息
    const imageSnapshot = (block.snapshot as any)?.image
    if (imageSnapshot) {
      console.log('📸 Snapshot 信息:', {
        token: imageSnapshot.token,
        name: imageSnapshot.name,
        hasDataUrl: !!imageSnapshot.dataUrl,
        dataUrlLength: imageSnapshot.dataUrl?.length || 0,
        width: imageSnapshot.width,
        height: imageSnapshot.height,
        mimeType: imageSnapshot.mimeType,
      })
      
      // 检查页面中是否有对应的图片元素
      if (imageSnapshot.token) {
        const imgElement = document.querySelector(`img[src*="${imageSnapshot.token}"]`)
        console.log('🌐 页面元素信息:', {
          found: !!imgElement,
          src: imgElement?.getAttribute('src')?.substring(0, 100) + '...',
          naturalWidth: (imgElement as HTMLImageElement)?.naturalWidth,
          naturalHeight: (imgElement as HTMLImageElement)?.naturalHeight,
          complete: (imgElement as HTMLImageElement)?.complete,
        })
      }
    } else {
      console.warn('⚠️ 没有找到图片 snapshot 数据')
    }
    
    console.groupEnd()
  })
  
  // 页面图片元素统计
  console.group('🌐 页面图片元素统计')
  const allImages = document.querySelectorAll('img')
  console.log(`📊 页面中共有 ${allImages.length} 个 img 元素`)
  
  const imagesByType = {
    dataUrl: 0,
    httpUrl: 0,
    blobUrl: 0,
    other: 0,
  }
  
  allImages.forEach((img, index) => {
    const src = img.getAttribute('src') || ''
    if (src.startsWith('data:')) {
      imagesByType.dataUrl++
    } else if (src.startsWith('http')) {
      imagesByType.httpUrl++
    } else if (src.startsWith('blob:')) {
      imagesByType.blobUrl++
    } else {
      imagesByType.other++
    }
    
    if (index < 5) { // 只显示前5个图片的详细信息
      console.log(`🖼️ 图片 ${index + 1}:`, {
        src: src.substring(0, 100) + (src.length > 100 ? '...' : ''),
        width: img.width,
        height: img.height,
        naturalWidth: (img as HTMLImageElement).naturalWidth,
        naturalHeight: (img as HTMLImageElement).naturalHeight,
        complete: (img as HTMLImageElement).complete,
      })
    }
  })
  
  console.log('📊 图片类型统计:', imagesByType)
  console.groupEnd()
  
  console.groupEnd()
}

/**
 * 收集所有图片块
 */
function collectImageBlocks(block: Blocks): Blocks[] {
  const imageBlocks: Blocks[] = []
  
  if (block.type === 'image') {
    imageBlocks.push(block)
  }
  
  if (block.children) {
    for (const child of block.children) {
      imageBlocks.push(...collectImageBlocks(child))
    }
  }
  
  return imageBlocks
}

/**
 * 调试工具：检查特定图片token在页面中的情况
 */
export function debugImageToken(token: string): void {
  console.group(`🔍 调试图片 token: ${token}`)
  
  // 尝试各种可能的选择器
  const selectors = [
    `img[src*="${token}"]`,
    `img[data-token="${token}"]`,
    `img[data-file-token="${token}"]`,
    `[data-token="${token}"] img`,
    `[data-file-token="${token}"] img`,
  ]
  
  let found = false
  selectors.forEach(selector => {
    const elements = document.querySelectorAll(selector)
    if (elements.length > 0) {
      console.log(`✅ 找到 ${elements.length} 个元素 (${selector}):`)
      elements.forEach((el, index) => {
        console.log(`  ${index + 1}.`, {
          tagName: el.tagName,
          src: el.getAttribute('src')?.substring(0, 100) + '...',
          dataToken: el.getAttribute('data-token'),
          dataFileToken: el.getAttribute('data-file-token'),
        })
      })
      found = true
    } else {
      console.log(`❌ 未找到元素 (${selector})`)
    }
  })
  
  if (!found) {
    console.warn('⚠️ 在页面中未找到任何匹配的图片元素')
    
    // 显示所有包含该token的元素
    const allElements = document.querySelectorAll('*')
    const matchingElements: Element[] = []
    allElements.forEach(el => {
      const text = el.outerHTML
      if (text.includes(token)) {
        matchingElements.push(el)
      }
    })
    
    if (matchingElements.length > 0) {
      console.log(`🔍 找到 ${matchingElements.length} 个包含该token的元素:`)
      matchingElements.slice(0, 3).forEach((el, index) => {
        console.log(`  ${index + 1}.`, {
          tagName: el.tagName,
          className: el.className,
          innerHTML: el.innerHTML.substring(0, 200) + '...',
        })
      })
    }
  }
  
  console.groupEnd()
}

/**
 * 调试工具：测试图片转换过程
 */
export async function debugImageConversion(imgElement: HTMLImageElement): Promise<void> {
  console.group('🧪 测试图片转换过程')
  
  console.log('📋 图片元素信息:', {
    src: imgElement.src.substring(0, 100) + '...',
    width: imgElement.width,
    height: imgElement.height,
    naturalWidth: imgElement.naturalWidth,
    naturalHeight: imgElement.naturalHeight,
    complete: imgElement.complete,
  })
  
  try {
    // 测试转换为data URL
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    
    if (!ctx) {
      console.error('❌ 无法获取canvas上下文')
      console.groupEnd()
      return
    }
    
    // 等待图片加载完成
    if (!imgElement.complete) {
      console.log('⏳ 等待图片加载完成...')
      await new Promise((resolve, reject) => {
        imgElement.onload = resolve
        imgElement.onerror = reject
        setTimeout(reject, 5000) // 5秒超时
      })
    }
    
    // 设置canvas尺寸
    canvas.width = imgElement.naturalWidth || imgElement.width
    canvas.height = imgElement.naturalHeight || imgElement.height
    
    console.log('🎨 Canvas 尺寸:', {
      width: canvas.width,
      height: canvas.height,
    })
    
    // 绘制图片到canvas
    ctx.drawImage(imgElement, 0, 0)
    
    // 转换为data URL
    const dataUrl = canvas.toDataURL('image/png')
    
    console.log('✅ 转换成功:', {
      dataUrlLength: dataUrl.length,
      dataUrlPrefix: dataUrl.substring(0, 50) + '...',
    })
    
  } catch (error) {
    console.error('❌ 转换失败:', error)
  }
  
  console.groupEnd()
}
