import { PageBlock, Blocks } from '@/pkg/lark/docx'

/**
 * 预处理图片，将图片转换为data URL
 */
export async function preprocessImages(rootBlock: PageBlock): Promise<void> {
  console.error('🖼️ 开始预处理图片...')

  const imageBlocks = collectImageBlocks(rootBlock)
  console.error(`🖼️ 发现 ${imageBlocks.length} 个图片块`)

  if (imageBlocks.length === 0) {
    console.error('🖼️ 没有图片需要处理')
    return
  }

  // 获取页面中所有图片元素，用于备用匹配
  const allPageImages = Array.from(document.querySelectorAll('img')) as HTMLImageElement[]
  console.error(`🖼️ 页面中共有 ${allPageImages.length} 个图片元素`)

  // 串行处理图片，避免并发问题
  for (let index = 0; index < imageBlocks.length; index++) {
    try {
      await processImageBlock(imageBlocks[index], index + 1, imageBlocks.length, allPageImages)
    } catch (error) {
      console.error(`🖼️ 处理图片 ${index + 1} 失败:`, error)
    }
  }

  console.error('🖼️ 图片预处理完成')
}

/**
 * 收集所有图片块
 */
function collectImageBlocks(block: Blocks): Blocks[] {
  const imageBlocks: Blocks[] = []

  if (block.type === 'image') {
    imageBlocks.push(block)
  }

  if (block.children) {
    for (const child of block.children) {
      imageBlocks.push(...collectImageBlocks(child))
    }
  }

  return imageBlocks
}

/**
 * 处理单个图片块
 */
async function processImageBlock(block: Blocks, index: number, total: number, allPageImages?: HTMLImageElement[]): Promise<void> {
  console.error(`🖼️ 处理图片 ${index}/${total}`)

  const imageSnapshot = (block.snapshot as any)?.image
  if (!imageSnapshot) {
    console.error(`🖼️ 图片 ${index} 没有snapshot数据`)
    return
  }

  const { token, name } = imageSnapshot
  if (!token) {
    console.error(`🖼️ 图片 ${index} 没有token`)
    return
  }

  try {
    // 尝试多种方式查找页面中对应的图片元素
    let imgElement = findImageElement(token, name, index, allPageImages)

    if (!imgElement) {
      console.error(`🖼️ 图片 ${index} 在页面中未找到对应元素`, { token, name })
      return
    }

    // 如果图片已经是data URL，跳过处理
    if (imgElement.src.startsWith('data:')) {
      console.error(`🖼️ 图片 ${index} 已经是data URL，跳过处理`)
      // 将data URL保存到snapshot中以便后续使用
      imageSnapshot.dataUrl = imgElement.src
      return
    }

    // 转换为data URL
    const dataUrl = await convertImageToDataUrl(imgElement)
    if (dataUrl) {
      // 保存data URL到snapshot中
      imageSnapshot.dataUrl = dataUrl
      console.error(`🖼️ 图片 ${index} 转换为data URL成功`)
    } else {
      console.error(`🖼️ 图片 ${index} 转换为data URL失败`)
    }
  } catch (error) {
    console.error(`🖼️ 处理图片 ${index} 时出错:`, error)
  }
}

/**
 * 将图片元素转换为data URL
 */
async function convertImageToDataUrl(imgElement: HTMLImageElement): Promise<string | null> {
  try {
    // 创建canvas来转换图片
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('无法获取canvas上下文')
    }

    // 等待图片加载完成
    await new Promise((resolve, reject) => {
      if (imgElement.complete) {
        resolve(void 0)
      } else {
        imgElement.onload = () => resolve(void 0)
        imgElement.onerror = reject
      }
    })

    // 设置canvas尺寸
    canvas.width = imgElement.naturalWidth || imgElement.width
    canvas.height = imgElement.naturalHeight || imgElement.height

    // 绘制图片到canvas
    ctx.drawImage(imgElement, 0, 0)

    // 转换为data URL
    return canvas.toDataURL('image/png')
  } catch (error) {
    console.error('转换图片为data URL失败:', error)

    // 尝试直接使用图片的src（如果是有效的data URL或blob URL）
    if (imgElement.src.startsWith('data:') || imgElement.src.startsWith('blob:')) {
      return imgElement.src
    }

    return null
  }
}

/**
 * 从data URL中提取图片数据
 */
export function extractImageDataFromDataUrl(dataUrl: string): {
  buffer: Uint8Array
  mimeType: string
  type: 'png' | 'jpg' | 'gif' | 'bmp'
} | null {
  try {
    if (!dataUrl.startsWith('data:')) {
      return null
    }

    // 提取MIME类型和base64数据
    const [mimeInfo, base64Data] = dataUrl.split(',')
    const mimeType = mimeInfo.match(/data:([^;]+)/)?.[1] || 'image/png'
    const buffer = Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))

    // 根据MIME类型确定图片格式
    let type: 'png' | 'jpg' | 'gif' | 'bmp' = 'png'
    if (mimeType.includes('jpeg') || mimeType.includes('jpg')) {
      type = 'jpg'
    } else if (mimeType.includes('gif')) {
      type = 'gif'
    } else if (mimeType.includes('bmp')) {
      type = 'bmp'
    }

    return {
      buffer,
      mimeType,
      type
    }
  } catch (error) {
    console.error('从data URL提取图片数据失败:', error)
    return null
  }
}

/**
 * 智能查找图片元素
 */
function findImageElement(token: string, name: string, index: number, allPageImages?: HTMLImageElement[]): HTMLImageElement | null {
  console.error(`🖼️ 开始查找图片 ${index}: token=${token}, name=${name}`)

  // 尝试多种选择器
  const selectors = [
    `img[src*="${token}"]`,
    `img[data-token="${token}"]`,
    `img[data-file-token="${token}"]`,
    `[data-token="${token}"] img`,
    `[data-file-token="${token}"] img`,
  ]

  for (const selector of selectors) {
    const element = document.querySelector(selector) as HTMLImageElement
    if (element) {
      console.error(`🖼️ 通过选择器找到图片: ${selector}`)
      return element
    }
  }

  // 如果通过token找不到，尝试通过文件名匹配
  if (name) {
    const imagesToSearch = allPageImages || Array.from(document.querySelectorAll('img'))
    for (const img of imagesToSearch) {
      const src = img.getAttribute('src') || ''
      const alt = img.getAttribute('alt') || ''
      const title = img.getAttribute('title') || ''

      // 检查src、alt或title中是否包含文件名
      if (src.includes(name) || alt.includes(name) || title.includes(name)) {
        console.error(`🖼️ 通过文件名找到图片: ${name}`)
        return img
      }
    }
  }

  // 最后尝试：按索引顺序匹配（假设图片在页面中的顺序与rootblock中的顺序一致）
  const imagesToSearch = allPageImages || Array.from(document.querySelectorAll('img'))
  if (index <= imagesToSearch.length) {
    const imgByIndex = imagesToSearch[index - 1]
    if (imgByIndex) {
      console.error(`🖼️ 通过索引找到图片: 第${index}个图片`)
      console.error(`🖼️ 图片信息:`, {
        src: imgByIndex.src.substring(0, 100) + '...',
        alt: imgByIndex.alt,
        title: imgByIndex.title,
        width: imgByIndex.width,
        height: imgByIndex.height,
      })
      return imgByIndex
    }
  }

  // 调试信息：显示所有图片元素
  console.error(`🖼️ 页面中共有 ${imagesToSearch.length} 个图片元素:`)
  imagesToSearch.forEach((img, i) => {
    console.error(`  ${i + 1}.`, {
      src: img.src.substring(0, 50) + '...',
      alt: img.alt,
      title: img.title,
      dataToken: img.getAttribute('data-token'),
      dataFileToken: img.getAttribute('data-file-token'),
    })
  })

  return null
}

/**
 * 获取图片的原始尺寸
 */
export async function getImageDimensions(dataUrl: string): Promise<{ width: number; height: number } | null> {
  try {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight
        })
      }
      img.onerror = () => {
        resolve(null)
      }
      img.src = dataUrl
    })
  } catch (error) {
    console.error('获取图片尺寸失败:', error)
    return null
  }
}
