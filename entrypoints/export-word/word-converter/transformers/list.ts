import { Blocks, BlockType } from '@/pkg/lark/docx'
import { WordContext, enterListLevel, exitListLevel, getAndIncrementOrderedListCounter } from '../context'
import { Paragraph, TextRun, Table } from 'docx'
import { transformChildren } from '../transform'

/**
 * 转换列表项块
 */
export async function transformList(block: Blocks, context: WordContext): Promise<Array<Paragraph | Table>> {
  const paragraphs: Array<Paragraph | Table> = []

  // 确定列表类型
  const listType = getListType(block.type)
  const textContent = extractListContent(block)

  // 进入列表层级
  enterListLevel(context, listType)

  try {
    // 创建列表项段落
    const listItemParagraph = createListItemParagraph(block, context, listType, textContent)
    if (listItemParagraph) {
      paragraphs.push(listItemParagraph)
    }

    // 处理子块（嵌套列表或其他内容）
    if (block.children && block.children.length > 0) {
      const childElements = await transformChildren(block.children, context)

      // 为子元素添加额外的缩进
      for (const element of childElements) {
        // 由于docx库的限制，我们无法直接修改已创建的段落
        // 这里简单地添加子元素
        paragraphs.push(element)
      }
    }
  } finally {
    // 退出列表层级
    exitListLevel(context)
  }

  return paragraphs
}

/**
 * 创建列表项段落
 */
function createListItemParagraph(
  block: Blocks,
  context: WordContext,
  listType: string,
  textContent: string
): Paragraph | null {
  if (!textContent.trim()) {
    return null
  }

  let bullet = ''
  let bulletColor = '0084FF' // 默认蓝色

  switch (listType) {
    case 'bullet':
      bullet = '• '
      break
    case 'ordered':
      const counter = getAndIncrementOrderedListCounter(context)
      bullet = `${counter}. `
      break
    case 'todo':
      // 检查是否已完成
      const isCompleted = checkTodoCompleted(block)
      bullet = isCompleted ? '☑ ' : '☐ '
      bulletColor = isCompleted ? '00AA00' : '666666'
      break
    default:
      bullet = '• '
  }

  const children: TextRun[] = []

  // 添加项目符号
  children.push(new TextRun({
    text: bullet,
    color: bulletColor,
    bold: listType === 'ordered',
  }))

  // 添加文本内容
  if (block.zoneState?.content?.ops) {
    // 处理富文本内容
    processListItemRichText(block.zoneState.content.ops, children)
  } else {
    // 普通文本
    children.push(new TextRun({
      text: textContent,
      ...(listType === 'todo' && checkTodoCompleted(block) && { strike: true }),
    }))
  }

  return new Paragraph({
    children,
    indent: {
      left: context.listLevel * 360, // 每层缩进0.25英寸
      hanging: 180, // 悬挂缩进，让项目符号突出
    },
    spacing: {
      after: 60, // 列表项间距 (3pt)
    },
  })
}

/**
 * 处理列表项的富文本内容
 */
function processListItemRichText(ops: any[], children: TextRun[]): void {
  for (const op of ops) {
    if (typeof op.insert === 'string') {
      const text = op.insert
      const attributes = op.attributes || {}

      children.push(new TextRun({
        text,
        bold: attributes.bold || false,
        italics: attributes.italic || false,
        underline: attributes.underline ? {} : undefined,
        strike: attributes.strike || false,
        color: attributes.color || undefined,
        size: attributes.size ? Math.round(attributes.size * 2) : undefined,
        font: attributes.font || undefined,
        highlight: attributes.background || undefined,
      }))
    } else if (op.insert && typeof op.insert === 'object') {
      // 处理嵌入对象
      if (op.insert.equation) {
        children.push(new TextRun({
          text: `[公式: ${op.insert.equation}]`,
          italics: true,
          color: '666666',
        }))
      } else if (op.insert.link) {
        children.push(new TextRun({
          text: op.insert.link.text || op.insert.link.url || '[链接]',
          color: '0066CC',
          underline: {},
        }))
      }
    }
  }
}

/**
 * 获取列表类型
 */
function getListType(blockType: BlockType): string {
  switch (blockType) {
    case BlockType.BULLET:
      return 'bullet'
    case BlockType.ORDERED:
      return 'ordered'
    case BlockType.TODO:
      return 'todo'
    default:
      return 'bullet'
  }
}

/**
 * 从列表块中提取文本内容
 */
function extractListContent(block: Blocks): string {
  // 优先使用zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
  }

  return ''
}

/**
 * 检查待办事项是否已完成
 */
function checkTodoCompleted(block: Blocks): boolean {
  // 尝试从block的属性中获取完成状态
  const todoBlock = block as any
  if (typeof todoBlock.completed === 'boolean') {
    return todoBlock.completed
  }

  // 尝试从snapshot中获取
  if (typeof todoBlock.snapshot?.completed === 'boolean') {
    return todoBlock.snapshot.completed
  }

  // 尝试从zoneState中获取
  if (typeof todoBlock.zoneState?.completed === 'boolean') {
    return todoBlock.zoneState.completed
  }

  return false
}
