import { Blocks } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, TextRun, Table } from 'docx'
import { transformChildren } from '../transform'

/**
 * 转换引用块
 */
export async function transformQuote(block: Blocks, context: WordContext): Promise<Array<Paragraph | Table>> {
  const paragraphs: Array<Paragraph | Table> = []

  // 处理引用的文本内容
  const textContent = extractQuoteContent(block)
  if (textContent.trim()) {
    paragraphs.push(new Paragraph({
      children: [
        new TextRun({
          text: textContent,
          italics: true,
          color: '666666',
        }),
      ],
      indent: {
        left: 720, // 0.5 inch 左缩进
      },
      spacing: {
        before: 120, // 段前间距 (6pt)
        after: 120,  // 段后间距 (6pt)
      },
      border: {
        left: {
          style: 'single',
          size: 12, // 较粗的左边框
          color: '0084FF', // 蓝色边框
        },
      },
    }))
  }

  // 处理子块
  if (block.children && block.children.length > 0) {
    const childElements = await transformChildren(block.children, context)

    // 为子元素添加缩进样式
    for (const element of childElements) {
      // 由于docx库的限制，我们无法直接修改已创建的段落
      // 这里简单地添加子元素，缩进效果通过其他方式实现
      paragraphs.push(element)
    }
  }

  return paragraphs
}

/**
 * 从引用块中提取文本内容
 */
function extractQuoteContent(block: Blocks): string {
  // 优先使用zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
  }

  return ''
}
