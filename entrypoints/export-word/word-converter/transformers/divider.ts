import { Blocks } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, TextRun } from 'docx'

/**
 * 转换分割线块
 */
export function transformDivider(block: Blocks, context: WordContext): Paragraph {
  return new Paragraph({
    children: [
      new TextRun({
        text: '─'.repeat(50), // 使用连字符创建分割线效果
        color: 'CCCCCC',
      })
    ],
    spacing: {
      before: 240, // 段前间距 (12pt)
      after: 240,  // 段后间距 (12pt)
    },
    border: {
      bottom: {
        style: 'single',
        size: 6,
        color: 'CCCCCC',
      },
    },
  })
}
