import { Blocks, BlockType } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, HeadingLevel } from 'docx'

/**
 * 转换标题块
 */
export function transformHeading(block: Blocks, context: WordContext): Paragraph | null {
  const textContent = extractTextFromHeading(block)
  if (!textContent.trim()) {
    return null
  }

  // 根据块类型确定标题级别
  const headingLevel = getHeadingLevel(block.type)
  if (!headingLevel) {
    // 如果不是有效的标题级别，作为普通段落处理
    return new Paragraph({
      text: textContent,
      spacing: {
        before: 240, // 段前间距 (12pt)
        after: 120,  // 段后间距 (6pt)
      },
    })
  }

  return new Paragraph({
    text: textContent,
    heading: headingLevel,
    spacing: {
      before: headingLevel === HeadingLevel.HEADING_1 ? 480 : 360, // H1更大的段前间距
      after: 240, // 标题后间距 (12pt)
    },
  })
}

/**
 * 从标题块中提取文本内容
 */
function extractTextFromHeading(block: Blocks): string {
  // 优先使用zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => {
        if (typeof op.insert === 'string') {
          return op.insert
        }
        return ''
      })
      .join('')
  }

  return ''
}

/**
 * 根据块类型获取标题级别
 */
function getHeadingLevel(blockType: BlockType): HeadingLevel | null {
  switch (blockType) {
    case BlockType.HEADING1:
      return HeadingLevel.HEADING_1
    case BlockType.HEADING2:
      return HeadingLevel.HEADING_2
    case BlockType.HEADING3:
      return HeadingLevel.HEADING_3
    case BlockType.HEADING4:
      return HeadingLevel.HEADING_4
    case BlockType.HEADING5:
      return HeadingLevel.HEADING_5
    case BlockType.HEADING6:
      return HeadingLevel.HEADING_6
    default:
      return null
  }
}
