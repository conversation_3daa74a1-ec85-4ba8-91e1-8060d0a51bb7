import { Blocks } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, TextRun, ImageRun } from 'docx'
import { transformChildren } from '../transform'
import { extractImageDataFromDataUrl, getImageDimensions } from '../image-processor'
import { calculateImageDimensions, addImageToContext } from '../context'

/**
 * 转换文本块
 */
export async function transformText(block: Blocks, context: WordContext): Promise<Paragraph | null> {
  // 如果有子块，递归处理子块
  if (block.children && block.children.length > 0) {
    const childElements = await transformChildren(block.children, context)
    // 文本块的子块应该合并到当前段落中，而不是作为独立元素
    // 这里我们只处理文本内容，子块中的其他元素会在其他地方处理
    // childElements 暂时不使用，因为文本块主要处理自身的文本内容
  }

  // 处理文本内容
  const textContent = extractTextFromBlock(block)
  if (!textContent.trim() && (!block.children || block.children.length === 0)) {
    return null
  }

  // 创建段落
  const children: (TextRun | ImageRun)[] = []

  // 处理富文本内容
  if (block.zoneState?.content?.ops) {
    await processRichTextOps(block.zoneState.content.ops, children, context)
  } else if (textContent.trim()) {
    // 如果没有富文本操作，使用纯文本
    children.push(new TextRun({ text: textContent }))
  }

  // 如果没有内容，返回空段落
  if (children.length === 0) {
    children.push(new TextRun({ text: '' }))
  }

  return new Paragraph({
    children,
    spacing: {
      after: 120, // 段后间距 (6pt)
    },
  })
}

/**
 * 从块中提取文本内容
 */
function extractTextFromBlock(block: Blocks): string {
  // 优先使用zoneState.allText
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 其次使用zoneState.content.ops
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => op.insert || '')
      .join('')
  }

  return ''
}

/**
 * 处理富文本操作
 */
async function processRichTextOps(ops: any[], children: (TextRun | ImageRun)[], context: WordContext): Promise<void> {
  for (const op of ops) {
    if (typeof op.insert === 'string') {
      // 文本内容
      const text = op.insert
      const attributes = op.attributes || {}

      children.push(new TextRun({
        text,
        bold: attributes.bold || false,
        italics: attributes.italic || false,
        underline: attributes.underline ? {} : undefined,
        strike: attributes.strike || false,
        color: attributes.color || undefined,
        size: attributes.size ? Math.round(attributes.size * 2) : undefined, // Word使用半点为单位
        font: attributes.font || undefined,
        highlight: attributes.background || undefined,
      }))
    } else if (op.insert && typeof op.insert === 'object') {
      // 嵌入对象（如图片、链接等）
      if (op.insert.image) {
        await processImageInsert(op.insert.image, children, context)
      } else if (op.insert.link) {
        processLinkInsert(op.insert.link, children, context)
      } else if (op.insert.equation) {
        // 数学公式转换为文本
        children.push(new TextRun({
          text: `[公式: ${op.insert.equation}]`,
          italics: true,
          color: '666666',
        }))
      }
    }
  }
}

/**
 * 处理图片插入
 */
async function processImageInsert(imageData: any, children: (TextRun | ImageRun)[], context: WordContext): Promise<void> {
  try {
    const { token, name, dataUrl } = imageData

    if (!dataUrl) {
      // 如果没有data URL，添加占位文本
      children.push(new TextRun({
        text: `[图片: ${name || '未知图片'}]`,
        italics: true,
        color: '999999',
      }))
      return
    }

    // 提取图片数据
    const imageInfo = extractImageDataFromDataUrl(dataUrl)
    if (!imageInfo) {
      children.push(new TextRun({
        text: `[图片: ${name || '未知图片'}]`,
        italics: true,
        color: '999999',
      }))
      return
    }

    // 获取图片尺寸
    const dimensions = await getImageDimensions(dataUrl)
    if (!dimensions) {
      children.push(new TextRun({
        text: `[图片: ${name || '未知图片'}]`,
        italics: true,
        color: '999999',
      }))
      return
    }

    // 计算合适的尺寸
    const targetDimensions = calculateImageDimensions(
      dimensions.width,
      dimensions.height,
      context
    )

    // 添加图片到上下文
    addImageToContext(context, token, name, dataUrl, targetDimensions.width, targetDimensions.height)

    // 创建图片运行
    children.push(new ImageRun({
      type: imageInfo.type,
      data: imageInfo.buffer,
      transformation: {
        width: targetDimensions.width,
        height: targetDimensions.height,
      },
    }))
  } catch (error) {
    console.error('处理图片插入失败:', error)
    children.push(new TextRun({
      text: `[图片处理失败]`,
      italics: true,
      color: 'FF0000',
    }))
  }
}

/**
 * 处理链接插入
 */
function processLinkInsert(linkData: any, children: (TextRun | ImageRun)[], _context: WordContext): void {
  const { url, text } = linkData

  // Word中的链接处理比较复杂，这里简化为带颜色和下划线的文本
  children.push(new TextRun({
    text: text || url || '[链接]',
    color: '0066CC',
    underline: {},
  }))
}
