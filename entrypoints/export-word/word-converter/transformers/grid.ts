import { Blocks } from '@/pkg/lark/docx'
import { WordContext } from '../context'
import { Paragraph, Table } from 'docx'
import { transformChildren } from '../transform'

/**
 * 转换网格布局块
 */
export async function transformGrid(block: Blocks, context: WordContext): Promise<Array<Paragraph | Table>> {
  // 网格布局在Word中比较复杂，这里简化处理
  // 将网格中的内容按顺序排列

  if (block.children && block.children.length > 0) {
    return await transformChildren(block.children, context)
  }

  return []
}
