import { PageBlock, Blocks, BlockType } from '@/pkg/lark/docx'
import { Document, Paragraph, Table } from 'docx'
import { createWordContext, WordContext } from './context'
import { transformBlock } from './transform'
import { preprocessImages } from './image-processor'

// 导出类型
export type {
  WordContext,
  WordOptions,
  WordTransformResult,
  BlockWordTransformer
} from './context'

// 导出转换器
export * from './transformers'

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET ||
      block.type === BlockType.ORDERED ||
      block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换顶级列表组
 */
async function convertTopLevelListGroup(listBlocks: Blocks[], listType: BlockType, context: WordContext): Promise<Array<Paragraph | Table>> {
  if (!listBlocks.length) return []

  const listItems: Array<Paragraph | Table> = []

  for (const block of listBlocks) {
    const result = await transformBlock(block, context)
    if (result) {
      if (Array.isArray(result)) {
        listItems.push(...result)
      } else {
        listItems.push(result)
      }
    }
  }

  return listItems
}

/**
 * 递归收集所有块的类型信息
 */
function collectAllBlockTypes(blocks: Blocks[], depth = 0): { type: string, depth: number, id?: number, hasChildren: boolean, textContent?: string, extraInfo?: any }[] {
  const result: { type: string, depth: number, id?: number, hasChildren: boolean, textContent?: string, extraInfo?: any }[] = []

  blocks.forEach((block, index) => {
    let extraInfo: any = undefined

    // 为不同类型的块收集特殊信息
    switch (block.type) {
      case 'view':
        extraInfo = {
          viewInfo: '文件容器块',
          childrenTypes: block.children?.map(child => child.type) || [],
          recordId: block.record?.id
        }
        break
      case 'file':
        const fileSnapshot = (block.snapshot as any)?.file
        extraInfo = {
          fileName: fileSnapshot?.name || '未知文件名',
          fileToken: fileSnapshot?.token || '无token',
          recordId: block.record?.id
        }
        break
      case 'image':
        const imageSnapshot = (block.snapshot as any)?.image
        extraInfo = {
          imageName: imageSnapshot?.name || '未知图片名',
          imageToken: imageSnapshot?.token || '无token',
          width: imageSnapshot?.width,
          height: imageSnapshot?.height,
          mimeType: imageSnapshot?.mimeType
        }
        break
      case 'table':
        const tableSnapshot = block.snapshot as any
        extraInfo = {
          rows: tableSnapshot?.rows_id?.length || 0,
          columns: tableSnapshot?.columns_id?.length || 0
        }
        break
      case 'code':
        const codeBlock = block as any
        extraInfo = {
          language: codeBlock?.language || '未知语言'
        }
        break
      case 'callout':
        extraInfo = {
          calloutInfo: '标注/提示块'
        }
        break
      case 'iframe':
        const iframeSnapshot = (block.snapshot as any)?.iframe
        extraInfo = {
          url: iframeSnapshot?.component?.url || '无URL',
          height: iframeSnapshot?.height || 0
        }
        break
      case 'whiteboard':
        const whiteboardSnapshot = block.snapshot as any
        extraInfo = {
          whiteboardInfo: '白板块',
          hasWhiteboardBlock: !!(block as any).whiteboardBlock
        }
        break
      default:
        // 对于其他类型，如果有特殊的snapshot数据也显示
        if ((block.snapshot as any) && Object.keys(block.snapshot).length > 1) {
          extraInfo = {
            snapshotKeys: Object.keys(block.snapshot).filter(key => key !== 'type')
          }
        }
        break
    }

    const info = {
      type: block.type,
      depth,
      id: block.id,
      hasChildren: block.children && block.children.length > 0,
      textContent: block.zoneState?.allText || undefined,
      extraInfo
    }
    result.push(info)

    // 递归处理子块
    if (block.children && block.children.length > 0) {
      const childResults = collectAllBlockTypes(block.children, depth + 1)
      result.push(...childResults)
    }
  })

  return result
}

/**
 * 打印所有块类型的调试信息
 */
function printBlockTypesDebugInfo(rootBlock: PageBlock): void {
  console.error('🔍 === Word导出 - 检查块结构 ===')

  if (!rootBlock.children || rootBlock.children.length === 0) {
    console.error('⚠️ rootBlock没有子块')
    return
  }

  // 收集所有块类型
  const allBlocks = collectAllBlockTypes(rootBlock.children)

  console.error(`📄 发现 ${allBlocks.length} 个块:`)
  allBlocks.forEach((block, index) => {
    const indent = '  '.repeat(block.depth)
    const extraText = block.textContent ? ` - "${block.textContent.slice(0, 30)}${block.textContent.length > 30 ? '...' : ''}"` : ''
    console.error(`${indent}${index + 1}. ${block.type}${extraText}`)
  })

  console.error('🔍 === 块结构检查完成 ===')
}

/**
 * 将 docx.rootBlock 直接转换为 Word 文档
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns Word文档对象
 */
export async function convertDocxToWordDocument(
  rootBlock: PageBlock | null,
  options: Partial<import('./context').WordOptions> = {}
): Promise<Document> {
  if (!rootBlock) {
    return new Document({
      sections: [{
        properties: {},
        children: [],
      }],
    })
  }

  console.error('🔄 开始Word转换 (convertDocxToWordDocument)')
  // 打印所有BlockType的调试信息
  printBlockTypesDebugInfo(rootBlock)

  // 预处理图片
  if (options.convertImages !== false) {
    await preprocessImages(rootBlock)
  }

  const context = createWordContext(options)
  const docElements: Array<Paragraph | Table> = []

  // 对顶级块进行列表分组处理
  const groupedBlocks = groupConsecutiveListBlocks(rootBlock.children)

  for (const group of groupedBlocks) {
    if (group.type === 'list' && group.listType) {
      // 处理顶级列表组
      const listElements = await convertTopLevelListGroup(group.blocks, group.listType, context)
      docElements.push(...listElements)
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const result = await transformBlock(child, context)
        if (result) {
          if (Array.isArray(result)) {
            docElements.push(...result)
          } else {
            docElements.push(result)
          }
        }
      }
    }
  }

  return new Document({
    sections: [{
      properties: {},
      children: docElements,
    }],
  })
}
