import { Blocks, BlockType } from '@/pkg/lark/docx'
import { WordContext } from './context'
import { Paragraph, Table } from 'docx'
import {
  transformText,
  transformHeading,
  transformDivider,
  transformCode,
  transformQuote,
  transformList,
  transformImage,
  transformTable,
  transformFile,
  transformWhiteboard,
  transformIframe,
  transformGrid
} from './transformers'

// 转换器映射
const transformers: Partial<Record<BlockType, (block: Blocks, context: WordContext) => Promise<Paragraph | Table | Array<Paragraph | Table> | null> | Paragraph | Table | Array<Paragraph | Table> | null>> = {
  [BlockType.TEXT]: transformText,
  [BlockType.HEADING1]: transformHeading,
  [BlockType.HEADING2]: transformHeading,
  [BlockType.HEADING3]: transformHeading,
  [BlockType.HEADING4]: transformHeading,
  [BlockType.HEADING5]: transformHeading,
  [BlockType.HEADING6]: transformHeading,
  [BlockType.HEADING7]: transformText, // 作为普通文本处理
  [BlockType.HEADING8]: transformText,
  [BlockType.HEADING9]: transformText,
  [BlockType.DIVIDER]: transformDivider,
  [BlockType.CODE]: transformCode,
  [BlockType.QUOTE_CONTAINER]: transformQuote,
  [BlockType.CALLOUT]: transformQuote,
  [BlockType.BULLET]: transformList,
  [BlockType.ORDERED]: transformList,
  [BlockType.TODO]: transformList,
  [BlockType.IMAGE]: transformImage,
  [BlockType.TABLE]: transformTable,
  [BlockType.CELL]: transformTable,
  [BlockType.FILE]: transformFile,
  [BlockType.WHITEBOARD]: transformWhiteboard,
  [BlockType.IFRAME]: transformIframe,
  [BlockType.GRID]: transformGrid,
  [BlockType.GRID_COLUMN]: transformGrid
}

/**
 * 转换单个块
 */
export async function transformBlock(block: Blocks, context: WordContext): Promise<Paragraph | Table | Array<Paragraph | Table> | null> {
  const transformer = transformers[block.type]

  if (transformer) {
    try {
      const result = transformer(block, context)
      return result instanceof Promise ? await result : result
    } catch (error) {
      console.error(`转换块失败 (${block.type}):`, error)
      return createFallbackElement(block, context)
    }
  }

  // 未知类型的降级处理
  return createFallbackElement(block, context)
}

/**
 * 转换子块列表
 */
export async function transformChildren(children: Blocks[], context: WordContext): Promise<Array<Paragraph | Table>> {
  const results: Array<Paragraph | Table> = []

  for (const child of children) {
    const result = await transformBlock(child, context)
    if (result) {
      if (Array.isArray(result)) {
        results.push(...result)
      } else {
        results.push(result)
      }
    }
  }

  return results
}

/**
 * 创建降级元素
 */
function createFallbackElement(block: Blocks, context: WordContext): Paragraph | null {
  const textContent = extractTextContent(block)

  if (!textContent.trim()) {
    return null
  }

  return new Paragraph({
    text: `[${block.type}] ${textContent}`,
  })
}

/**
 * 提取块的文本内容
 */
function extractTextContent(block: Blocks): string {
  // 尝试从zoneState中提取文本
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => op.insert || '')
      .join('')
  }

  // 尝试从zoneState.allText中提取
  if (block.zoneState?.allText) {
    return block.zoneState.allText
  }

  // 递归提取子块的文本内容
  if (block.children && block.children.length > 0) {
    return block.children
      .map(child => extractTextContent(child))
      .join(' ')
  }

  return ''
}
