# Word 转换器

这是一个直接从飞书文档的 rootblock 结构生成 Word 文档的转换器，参考了 `html-converter-fn` 的设计模式。

## 架构设计

### 核心文件

- `index.ts` - 主转换函数和入口点
- `context.ts` - 转换上下文和配置管理
- `transform.ts` - 块转换的核心逻辑
- `image-processor.ts` - 图片预处理和转换
- `transformers/` - 各种块类型的具体转换器

### 转换器目录

- `text.ts` - 文本块转换器
- `heading.ts` - 标题块转换器
- `divider.ts` - 分割线转换器
- `code.ts` - 代码块转换器
- `quote.ts` - 引用块转换器
- `list.ts` - 列表转换器
- `image.ts` - 图片转换器
- `table.ts` - 表格转换器
- `file.ts` - 文件转换器
- `whiteboard.ts` - 白板转换器
- `iframe.ts` - iframe转换器
- `grid.ts` - 网格布局转换器

## 使用方法

```typescript
import { convertDocxToWordDocument } from './word-converter'
import { docx } from '@/pkg/lark/docx'

// 转换文档
const doc = await convertDocxToWordDocument(docx.rootBlock, {
  convertImages: true,
  convertFiles: false,
  maxImageWidth: 400,
  maxImageHeight: 600,
  keepImageAspectRatio: true,
})

// 生成Word文件
const buffer = await Packer.toBlob(doc)
```

## 配置选项

```typescript
interface WordOptions {
  /** 是否转换图片 */
  convertImages: boolean
  /** 是否转换文件 */
  convertFiles: boolean
  /** 图片最大宽度（像素） */
  maxImageWidth: number
  /** 图片最大高度（像素） */
  maxImageHeight: number
  /** 是否保持原始图片尺寸比例 */
  keepImageAspectRatio: boolean
}
```

## 特性

### 支持的块类型

- ✅ 文本块（支持富文本格式）
- ✅ 标题块（H1-H6）
- ✅ 代码块（支持语言标识）
- ✅ 引用块
- ✅ 列表（有序、无序、待办）
- ✅ 图片（自动转换为data URL）
- ✅ 表格
- ✅ 分割线
- ⚠️ 文件（显示占位符）
- ⚠️ 白板（显示占位符）
- ⚠️ iframe（显示占位符）
- ✅ 网格布局（简化处理）

### 图片处理

- 自动将图片转换为data URL
- 智能尺寸调整以适应Word页面
- 保持图片宽高比
- 支持多种图片格式（PNG、JPG、GIF、BMP）

### 列表处理

- 支持嵌套列表
- 自动处理列表编号
- 支持待办事项的完成状态
- 保持列表层级缩进

### 样式处理

- 保持原始文档的格式
- 支持粗体、斜体、下划线等文本样式
- 代码块使用等宽字体和背景色
- 表格自动添加边框和表头样式

## 与HTML转换器的区别

1. **直接转换**: 不经过HTML中间步骤，直接从rootblock转换为Word元素
2. **更好的格式保持**: 避免了HTML解析可能带来的格式丢失
3. **原生Word元素**: 生成的是原生的docx元素，而不是从HTML解析的结果
4. **更好的性能**: 减少了HTML生成和解析的开销

## 扩展指南

### 添加新的块类型转换器

1. 在 `transformers/` 目录下创建新的转换器文件
2. 实现转换函数：
   ```typescript
   export function transformNewBlock(block: Blocks, context: WordContext): Paragraph | Table | Array<Paragraph | Table> | null {
     // 转换逻辑
   }
   ```
3. 在 `transformers/index.ts` 中导出
4. 在 `transform.ts` 的转换器映射中添加

### 自定义样式

可以通过修改各个转换器中的样式配置来自定义输出格式，例如：

```typescript
// 在 heading.ts 中自定义标题样式
return new Paragraph({
  text: textContent,
  heading: headingLevel,
  spacing: {
    before: 480, // 自定义段前间距
    after: 240,  // 自定义段后间距
  },
})
```

## 注意事项

1. 图片转换需要浏览器环境支持Canvas API
2. 大型文档可能需要较长的转换时间
3. 某些复杂的布局可能会被简化处理
4. 文件和白板等特殊内容会显示为占位符文本
