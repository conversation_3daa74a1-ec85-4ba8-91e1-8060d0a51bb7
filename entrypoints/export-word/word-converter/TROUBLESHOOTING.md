# Word 转换器故障排除指南

## 图片问题诊断

### 常见错误信息

1. **"[图片未加载: image.png]"**
   - 原因：图片在预处理阶段未能成功转换为 data URL
   - 解决方案：检查图片是否在页面中正确加载

2. **"[图片数据缺失]"**
   - 原因：rootblock 中的图片块没有 snapshot 数据
   - 解决方案：确保文档已完全加载

3. **"[图片token缺失]"**
   - 原因：图片 snapshot 中没有 token 信息
   - 解决方案：检查图片块的数据结构

4. **"[图片格式不支持]"**
   - 原因：无法从 data URL 中提取图片数据
   - 解决方案：检查 data URL 格式是否正确

## 调试步骤

### 1. 启用调试模式

在浏览器控制台中运行以下代码来查看详细的调试信息：

```javascript
// 查看所有图片块的详细信息
import { debugImageBlocks } from './debug-utils'
debugImageBlocks(docx.rootBlock)

// 调试特定图片token
import { debugImageToken } from './debug-utils'
debugImageToken('your-image-token-here')

// 测试图片转换过程
import { debugImageConversion } from './debug-utils'
const imgElement = document.querySelector('img')
debugImageConversion(imgElement)
```

### 2. 检查控制台输出

Word 转换器会在控制台输出详细的调试信息：

- `🔍 === Word导出 - 检查块结构 ===` - 显示所有块的类型和结构
- `🔍 === 图片块调试信息 ===` - 显示图片块的详细信息
- `🖼️ 开始预处理图片...` - 图片预处理过程
- `🖼️ 图片转换失败:` - 图片转换错误信息

### 3. 常见问题和解决方案

#### 问题：图片在页面中找不到

**症状：**
```
🖼️ 图片 1 在页面中未找到对应元素
🖼️ 尝试查找的选择器: img[src*="token"]
```

**解决方案：**
1. 确保页面已完全加载
2. 检查图片是否被懒加载
3. 尝试滚动到图片位置使其加载
4. 检查图片是否在 iframe 中

#### 问题：图片转换为 data URL 失败

**症状：**
```
🖼️ 图片 1 转换为data URL失败
转换图片为data URL失败: SecurityError
```

**解决方案：**
1. 检查图片的跨域策略
2. 确保图片已完全加载 (`img.complete === true`)
3. 尝试等待更长时间让图片加载完成

#### 问题：图片尺寸获取失败

**症状：**
```
无法获取图片尺寸: image.png
```

**解决方案：**
1. 检查图片的 `naturalWidth` 和 `naturalHeight` 属性
2. 确保图片格式正确
3. 检查 data URL 是否有效

### 4. 手动修复步骤

如果自动转换失败，可以尝试以下手动修复步骤：

#### 步骤 1：检查图片状态
```javascript
// 检查所有图片的加载状态
document.querySelectorAll('img').forEach((img, index) => {
  console.log(`图片 ${index + 1}:`, {
    src: img.src.substring(0, 50) + '...',
    complete: img.complete,
    naturalWidth: img.naturalWidth,
    naturalHeight: img.naturalHeight
  })
})
```

#### 步骤 2：强制加载图片
```javascript
// 强制重新加载所有图片
document.querySelectorAll('img').forEach(img => {
  if (!img.complete) {
    img.src = img.src // 触发重新加载
  }
})
```

#### 步骤 3：等待图片加载完成
```javascript
// 等待所有图片加载完成后再导出
const waitForImages = () => {
  const images = document.querySelectorAll('img')
  const promises = Array.from(images).map(img => {
    if (img.complete) return Promise.resolve()
    return new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
      setTimeout(reject, 10000) // 10秒超时
    })
  })
  return Promise.all(promises)
}

waitForImages().then(() => {
  console.log('所有图片已加载完成，可以开始导出')
  // 在这里调用导出函数
})
```

### 5. 配置选项调整

如果图片问题持续存在，可以调整转换选项：

```javascript
// 禁用图片转换
const doc = await convertDocxToWordDocument(docx.rootBlock, {
  convertImages: false, // 禁用图片转换，显示占位符
  convertFiles: false,
})

// 或者调整图片尺寸限制
const doc = await convertDocxToWordDocument(docx.rootBlock, {
  convertImages: true,
  maxImageWidth: 300,  // 减小最大宽度
  maxImageHeight: 400, // 减小最大高度
  keepImageAspectRatio: true,
})
```

### 6. 性能优化

对于包含大量图片的文档：

1. **分批处理图片**：修改 `preprocessImages` 函数，分批处理图片而不是并行处理所有图片
2. **增加超时时间**：给图片加载更多时间
3. **降低图片质量**：使用较低的图片质量设置

### 7. 浏览器兼容性

不同浏览器可能有不同的行为：

- **Chrome**: 通常工作良好
- **Firefox**: 可能需要更长的图片加载时间
- **Safari**: 对跨域图片有更严格的限制
- **Edge**: 行为类似 Chrome

### 8. 联系支持

如果问题仍然存在，请提供以下信息：

1. 浏览器版本和操作系统
2. 控制台的完整错误日志
3. 问题图片的类型和大小
4. 文档的大致结构（图片数量、文档大小等）

通过这些调试工具和步骤，应该能够诊断和解决大部分图片相关的问题。
