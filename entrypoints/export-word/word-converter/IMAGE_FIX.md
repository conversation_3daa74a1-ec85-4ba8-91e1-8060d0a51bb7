# 图片问题修复方案

## 问题描述

根据控制台日志分析，发现以下问题：

```
🖼️ 图片 3 在页面中未找到对应元素 {token: 'A9s8bTecWo7KipxeOjEcv1CRnkf', name: 'image.png'}
🖼️ 尝试查找的选择器: img[src*="A9s8bTecWo7KipxeOjEcv1CRnkf"]
🖼️ 页面中共有 3 个图片元素
```

**问题分析：**
- 系统发现了3个图片块
- 前2个图片成功转换为data URL
- 第3个图片的token在页面中找不到匹配的图片元素
- 页面中确实有3个图片元素，但token不匹配

## 解决方案

### 1. 智能图片匹配策略

实现了多层级的图片查找策略：

#### 第一层：Token匹配
```javascript
const selectors = [
  `img[src*="${token}"]`,
  `img[data-token="${token}"]`,
  `img[data-file-token="${token}"]`,
  `[data-token="${token}"] img`,
  `[data-file-token="${token}"] img`,
]
```

#### 第二层：文件名匹配
```javascript
// 检查src、alt或title中是否包含文件名
if (src.includes(name) || alt.includes(name) || title.includes(name)) {
  return img
}
```

#### 第三层：索引顺序匹配
```javascript
// 按索引顺序匹配（假设图片在页面中的顺序与rootblock中的顺序一致）
const imgByIndex = imagesToSearch[index - 1]
```

### 2. 改进的预处理流程

#### 串行处理
- 将并行处理改为串行处理，避免并发问题
- 确保图片处理的顺序性

#### 统一图片元素获取
- 在预处理开始时获取所有页面图片元素
- 传递给每个图片处理函数，确保一致性

### 3. 增强的调试信息

#### 详细的查找过程日志
```javascript
console.error(`🖼️ 开始查找图片 ${index}: token=${token}, name=${name}`)
console.error(`🖼️ 通过选择器找到图片: ${selector}`)
console.error(`🖼️ 通过文件名找到图片: ${name}`)
console.error(`🖼️ 通过索引找到图片: 第${index}个图片`)
```

#### 图片元素详细信息
```javascript
console.error(`🖼️ 图片信息:`, {
  src: imgByIndex.src.substring(0, 100) + '...',
  alt: imgByIndex.alt,
  title: imgByIndex.title,
  width: imgByIndex.width,
  height: imgByIndex.height,
})
```

## 代码变更

### 主要文件修改

1. **image-processor.ts**
   - 更新 `preprocessImages` 函数，改为串行处理
   - 更新 `processImageBlock` 函数，接受页面图片数组参数
   - 新增 `findImageElement` 函数，实现智能图片匹配

### 关键改进

#### 1. 串行处理避免竞争条件
```javascript
// 串行处理图片，避免并发问题
for (let index = 0; index < imageBlocks.length; index++) {
  await processImageBlock(imageBlocks[index], index + 1, imageBlocks.length, allPageImages)
}
```

#### 2. 多策略图片查找
```javascript
function findImageElement(token: string, name: string, index: number, allPageImages?: HTMLImageElement[]): HTMLImageElement | null {
  // 1. 尝试token匹配
  // 2. 尝试文件名匹配  
  // 3. 尝试索引匹配
  // 4. 输出调试信息
}
```

#### 3. 统一的图片元素管理
```javascript
// 获取页面中所有图片元素，用于备用匹配
const allPageImages = Array.from(document.querySelectorAll('img')) as HTMLImageElement[]
```

## 预期效果

### 解决的问题

1. **Token不匹配问题** - 通过多种匹配策略解决
2. **图片顺序问题** - 通过索引匹配作为备用方案
3. **调试困难问题** - 通过详细日志快速定位问题

### 提升的体验

1. **更高的成功率** - 多层级匹配策略提高图片识别成功率
2. **更好的调试** - 详细的日志帮助快速定位问题
3. **更稳定的处理** - 串行处理避免并发问题

## 使用建议

### 1. 查看控制台日志
新的实现会输出详细的图片处理日志，包括：
- 图片查找过程
- 匹配策略使用情况
- 图片元素详细信息

### 2. 等待页面完全加载
确保在导出前所有图片都已完全加载：
```javascript
// 等待所有图片加载完成
const waitForImages = () => {
  const images = document.querySelectorAll('img')
  const promises = Array.from(images).map(img => {
    if (img.complete) return Promise.resolve()
    return new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
      setTimeout(reject, 10000) // 10秒超时
    })
  })
  return Promise.all(promises)
}
```

### 3. 备用方案
如果图片问题仍然存在，可以临时禁用图片转换：
```javascript
const doc = await convertDocxToWordDocument(docx.rootBlock, {
  convertImages: false, // 禁用图片转换
})
```

## 测试验证

### 测试场景

1. **正常情况** - token匹配成功
2. **Token不匹配** - 文件名匹配成功
3. **文件名不匹配** - 索引匹配成功
4. **所有策略失败** - 显示占位符文本

### 验证方法

1. 查看控制台日志确认匹配策略
2. 检查生成的Word文档中的图片
3. 验证图片顺序是否正确

通过这些改进，应该能够显著提高图片处理的成功率，并且在出现问题时能够快速定位和解决。
