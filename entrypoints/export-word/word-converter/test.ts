// 简单测试文件，验证Word转换器是否正常工作
import { convertDocxToWordDocument } from './index'
import { Document } from 'docx'

// 模拟一个简单的rootBlock用于测试
const mockRootBlock = {
  id: 1,
  type: 'page' as const,
  children: [
    {
      id: 2,
      type: 'text' as const,
      zoneState: {
        allText: '这是一个测试文档',
        content: {
          ops: [
            {
              insert: '这是一个测试文档'
            }
          ]
        }
      },
      snapshot: {},
      record: { id: '2' }
    },
    {
      id: 3,
      type: 'heading1' as const,
      zoneState: {
        allText: '标题1',
        content: {
          ops: [
            {
              insert: '标题1'
            }
          ]
        }
      },
      snapshot: {},
      record: { id: '3' }
    }
  ],
  zoneState: {
    allText: '文档标题',
    content: {
      ops: [
        {
          insert: '文档标题'
        }
      ]
    }
  },
  snapshot: {},
  record: { id: '1' }
}

// 测试函数
export async function testWordConverter(): Promise<boolean> {
  try {
    console.log('🧪 开始测试Word转换器...')
    
    const doc = await convertDocxToWordDocument(mockRootBlock, {
      convertImages: false,
      convertFiles: false,
    })
    
    // 检查返回的是否是Document实例
    if (doc instanceof Document) {
      console.log('✅ Word转换器测试成功！')
      return true
    } else {
      console.error('❌ Word转换器返回的不是Document实例')
      return false
    }
  } catch (error) {
    console.error('❌ Word转换器测试失败:', error)
    return false
  }
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testWordConverter().then(success => {
    process.exit(success ? 0 : 1)
  })
}
