import { Toast } from "@/pkg/lark/env";
import { prepareExportDataForPdf } from "../export-pdf/prepare-util";
import { confirmWithCancel } from '@/pkg/utils/notification'
import { fileSave, supported } from 'browser-fs-access'
import { convertDocxToWordDocument } from "./word-converter";
import { docx } from "@/pkg/lark/docx";
import { Packer } from 'docx'

export async function exportWordInjected() {
  // 使用PDF导出的数据准备方法
  const data = await prepareExportDataForPdf()
  if (!data) return

  const { recommendName, recoverScrollTop } = data

  // 生成带时间戳的文件名
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hour = now.getHours().toString().padStart(2, '0')
  const minute = now.getMinutes().toString().padStart(2, '0')
  const second = now.getSeconds().toString().padStart(2, '0')
  const timestamp = `${year}${month}${day}_${hour}${minute}${second}`
  const filename = `${recommendName}_${timestamp}.docx`

  const toBlobContent = async (): Promise<Blob | null> => {
    // 第一阶段：准备数据
    Toast.loading({
      content: '正在准备Word文档数据（1/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 第二阶段：直接从rootblock转换为Word文档
    Toast.loading({
      content: '正在处理文档内容和图片（2/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    // 第三阶段：生成Word文件
    Toast.loading({
      content: '正在生成Word文件（3/3）',
      keepAlive: true,
      key: 'DOWNLOADING',
    })

    try {
      // 直接从rootblock转换为Word文档
      const doc = await convertDocxToWordDocument(docx.rootBlock, {
        convertImages: true,
        convertFiles: false, // Word导出不处理文件下载
      })

      // 生成blob
      const buffer = await Packer.toBlob(doc)
      return buffer
    } catch (error) {
      console.error('❌ docx转换失败:', error)
      Toast.error({ content: 'Word文档生成失败，请重试' })
      recoverScrollTop?.()
      return null
    }
  }

  if (!supported) {
    Toast.error({ content: '当前浏览器不支持文件保存功能，请使用现代浏览器' })
    recoverScrollTop?.()
    return
  }

  try {
    // 先生成所有内容
    console.error('📁 开始生成Word文档内容...')
    const blob = await toBlobContent()

    // 检查blob是否成功生成
    if (!blob) {
      console.error('❌ Word文档内容生成失败')
      Toast.error({ content: 'Word文档生成失败，请重试' })
      recoverScrollTop?.()
      return
    }

    console.error('📁 Word文档内容生成完成，准备保存文件')

    // 检查用户激活状态，如果失效则重新激活
    if (!navigator.userActivation?.isActive) {
      console.error('📁 用户激活状态失效，需要重新激活')
      // 用户激活状态失效时，先获取用户确认来重新激活
      const confirmed = await confirmWithCancel('文件已准备完成，是否保存Word文件？')
      if (!confirmed) {
        recoverScrollTop?.()
        return
      }
    }

    console.error('📁 开始保存Word文件...')
    await fileSave(blob, {
      fileName: filename,
      extensions: ['.docx'],
    })
    console.error('📁 Word文件保存成功')

    Toast.success({ content: '导出成功' })
  } catch (error) {
    console.error('📁 Word文件保存失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'

    if (errorMessage.includes('user gesture') || errorMessage.includes('User activation')) {
      Toast.error({
        content: '文件保存需要用户操作，请重新点击导出按钮'
      })
    } else {
      Toast.error({
        content: `文件保存失败: ${errorMessage}`
      })
    }
  } finally {
    Toast.remove('DOWNLOADING')
    recoverScrollTop?.()
  }
}