import { defineProxyService, flattenPromise } from '@webext-core/proxy-service';
import { IDBPDatabase } from 'idb';
interface Todo {
  id: string;
  title: string;
  completed: boolean;
  data: string;
  originalUrl?: string;
}
export const [registerTodosRepo, getTodosRepo] = defineProxyService('TodosRepo', createTodosRepo)

function createTodosRepo(idbPromise: Promise<IDBPDatabase>) {
  const idb = flattenPromise(idbPromise);

  return {
    async create(todo: Todo): Promise<void> {
      await idb.add('todos', todo);
    },
    getOne(id: string): Promise<Todo> {
      return idb.get('todos', id);
    },
    getAll(): Promise<Todo[]> {
      return idb.getAll('todos');
    },
    async update(todo: Todo): Promise<void> {
      await idb.put('todos', todo);
    },
    async delete(todo: Todo): Promise<void> {
      await idb.delete('todos', todo.id);
    },
  };
}