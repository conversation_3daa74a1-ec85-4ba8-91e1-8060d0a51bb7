// 图片优化配置接口
export interface ImageOptimizationConfig {
  maxWidth: number
  maxHeight: number
  webpQuality: number
  jpegQuality: number
  enableWebP: boolean
}

// 不同导出类型的优化配置
export const IMAGE_CONFIGS = {
  // PDF导出：平衡质量和文件大小
  pdf: {
    maxWidth: 1200,
    maxHeight: 1200,
    webpQuality: 0.8,
    jpegQuality: 0.85,
    enableWebP: false
  } as ImageOptimizationConfig,

  // 图片导出：保持较高质量
  image: {
    maxWidth: 1600,
    maxHeight: 1600,
    webpQuality: 0.9,
    jpegQuality: 0.9,
    enableWebP: false // 图片导出通常希望保持原格式兼容性
  } as ImageOptimizationConfig,

  // Word导出：保持高质量，不压缩，确保兼容性
  word: {
    maxWidth: 2000,
    maxHeight: 2000,
    webpQuality: 0.95,
    jpegQuality: 0.95,
    enableWebP: false // Word文档需要最大兼容性
  } as ImageOptimizationConfig
}

// 默认配置（用于向后兼容）
export const DEFAULT_IMAGE_CONFIG = IMAGE_CONFIGS.pdf

// 图片优化函数：压缩和格式转换
export const optimizeImageToDataUrl = async (blob: Blob, config: ImageOptimizationConfig = DEFAULT_IMAGE_CONFIG): Promise<string> => {
  return new Promise((resolve) => {
    const img = new Image()
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    img.onload = () => {
      // 计算优化后的尺寸
      let { width, height } = img

      // 如果图片过大，按比例缩放
      if (width > config.maxWidth || height > config.maxHeight) {
        const ratio = Math.min(config.maxWidth / width, config.maxHeight / height)
        width = Math.round(width * ratio)
        height = Math.round(height * ratio)
      }

      // 设置canvas尺寸
      canvas.width = width
      canvas.height = height

      // 绘制图片到canvas
      ctx.drawImage(img, 0, 0, width, height)

      // 构建格式列表，根据配置决定是否包含WebP
      const formats: Array<{ type: string, quality?: number }> = []

      if (config.enableWebP) {
        formats.push({ type: 'image/webp', quality: config.webpQuality })
      }
      formats.push({ type: 'image/jpeg', quality: config.jpegQuality })
      formats.push({ type: 'image/png', quality: undefined })

      let bestDataUrl = ''
      let minSize = Infinity

      for (const format of formats) {
        try {
          const dataUrl = canvas.toDataURL(format.type, format.quality)
          if (dataUrl.length < minSize) {
            minSize = dataUrl.length
            bestDataUrl = dataUrl
          }
        } catch (e) {
          // 某些浏览器可能不支持webp，忽略错误继续
          continue
        }
      }

      // 如果所有格式都失败，使用原始blob
      if (!bestDataUrl) {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result as string)
        reader.readAsDataURL(blob)
        return
      }

      // 计算压缩比例并记录日志
      const originalSize = blob.size
      const optimizedSize = Math.round(bestDataUrl.length * 0.75) // base64大约比原始数据大33%
      const compressionRatio = ((originalSize - optimizedSize) / originalSize * 100).toFixed(1)
      console.error(`📊 图片优化: ${originalSize}B → ${optimizedSize}B (压缩${compressionRatio}%)`)

      resolve(bestDataUrl)

      // 清理资源
      URL.revokeObjectURL(img.src)
    }

    img.onerror = () => {
      // 如果图片加载失败，回退到原始方法
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.readAsDataURL(blob)
    }

    // 加载图片
    img.src = URL.createObjectURL(blob)
  })
}

// 并行处理单个图片的函数 - 优化版本，支持压缩和格式转换
export const processImageParallel = async (image: any, index: number, total: number, config: ImageOptimizationConfig): Promise<void> => {
  if (!image.data?.fetchSources && !image.data?.fetchBlob) return

  try {
    let blob: Blob | null = null

    if (image.data.fetchBlob) {
      // 处理白板图片
      blob = await image.data.fetchBlob()
    } else if (image.data.fetchSources) {
      // 处理普通图片
      const sources = await image.data.fetchSources()
      if (sources?.src) {
        const response = await fetch(sources.src)
        blob = await response.blob()
      }
    }

    if (!blob) return

    // 优化图片：压缩和格式转换
    const optimizedDataUrl = await optimizeImageToDataUrl(blob, config)
    image.url = optimizedDataUrl

    console.error(`✅ 图片 ${index + 1}/${total} 处理完成`)
  } catch (error) {
    console.error(`❌ 图片 ${index + 1}/${total} 处理失败:`, error)
  }
}

// 原始的图片处理函数（用于Word导出）
export const processImageParallelOriginal = async (image: any, index: number, total: number): Promise<void> => {
  if (!image.data?.fetchSources && !image.data?.fetchBlob) return

  try {
    if (image.data.fetchBlob) {
      // 处理白板图片
      const blob = await image.data.fetchBlob()
      if (blob) {
        const dataUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result as string)
          reader.readAsDataURL(blob)
        })
        image.url = dataUrl
      }
    } else if (image.data.fetchSources) {
      // 处理普通图片
      const sources = await image.data.fetchSources()
      if (sources?.src) {
        const response = await fetch(sources.src)
        const blob = await response.blob()
        const dataUrl = await new Promise<string>((resolve) => {
          const reader = new FileReader()
          reader.onload = () => resolve(reader.result as string)
          reader.readAsDataURL(blob)
        })
        image.url = dataUrl
      }
    }
    console.error(`✅ 图片 ${index + 1}/${total} 处理完成`)
  } catch (error) {
    console.error(`❌ 图片 ${index + 1}/${total} 处理失败:`, error)
  }
}

// 将blob转换为dataURL的通用函数
export const blobToDataUrl = (blob: Blob): Promise<string> => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.readAsDataURL(blob)
  })
}
