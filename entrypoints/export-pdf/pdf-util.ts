import { docx } from "@/pkg/lark/docx";
import { convertDocxToHtmlForPdf } from "@/pkg/lark/html-converter-fn";
import { Toast } from "@/pkg/lark/env";
import { exportOriginalPDF } from "./origin-pdf-export";
import { prepareExportDataForPdf } from "./prepare-util";

export async function exportPdfInjected() {
  // 获取head里面的name="title" 的meta 标签的content属性
  const title = document
    .querySelector('meta[name="title"]')
    ?.getAttribute("content");
  const title2 = document.querySelector("title")?.textContent;
  const isPdfFile1 = title && title.endsWith(".pdf");
  const isPdfFile2 = title2 && title2.includes(".pdf");
  if (isPdfFile1 || isPdfFile2) {
    console.error("导出原pdf文档");
    await exportOriginalPDF();
    return;
  }

  console.error("🚀 开始PDF导出流程 (优化版)");
  const startTime = performance.now();

  // 使用PDF专用的优化数据准备逻辑（不需要Markdown AST转换和图片URL生成）
  const data = await prepareExportDataForPdf();
  if (!data) return;

  const { recommendName, recoverScrollTop } = data;
  const prepareTime = performance.now();
  console.error(`⏱️ 数据准备耗时: ${(prepareTime - startTime).toFixed(2)}ms`);

  // 在用户确认前预处理HTML内容
  Toast.loading({
    content: "正在生成PDF预览页面...",
    keepAlive: true,
    key: "pdf-export",
  });

  // 使用新的HTML转换器直接转换，等待图片处理完成
  const htmlResult = await convertDocxToHtmlForPdf(docx.rootBlock, {
    useInlineStyles: true, // PDF导出使用内联样式，避免样式丢失
    cssClassPrefix: "feishu", // 统一CSS类名前缀
    convertImages: true, // 转换图片
    convertFiles: true, // 转换文件链接
    generateToc: true, // 生成目录
    tocTitle: "目录", // 目录标题
    tocMaxLevel: 6, // 目录最大层级
  });

  const convertTime = performance.now();
  console.error(`⏱️ HTML转换耗时: ${(convertTime - prepareTime).toFixed(2)}ms`);
  console.error("🎉 使用新的HTML转换器转换完成！");
  console.error("- 转换器类型: 直接HTML转换 (跳过 Markdown AST)");
  console.error("- 图片处理: 直接转换为dataURL (跳过公开URL生成)");
  console.error("- HTML长度:", htmlResult.html.length);
  console.error("- 图片数量:", htmlResult.images.length);
  console.error("- 文件数量:", htmlResult.files.length);
  console.error("- 目录项数量:", htmlResult.toc.length);

  const tempDiv = document.createElement("div");
  // 使用带目录的HTML，如果目录存在的话
  tempDiv.innerHTML = htmlResult.htmlWithToc || htmlResult.html;

  Toast.remove("pdf-export");

  const totalTime = performance.now();
  console.error(`⏱️ 总处理耗时: ${(totalTime - startTime).toFixed(2)}ms`);

  try {
    window.postMessage(
      {
        action: "preview",
        data: htmlResult.htmlWithToc,
        title: recommendName,
        originalUrl: window.location.href,
      },
      "*"
    );
  } catch (error) {
    Toast.error({ content: "❌ 打开预览页面失败，请重试" });
  } finally {
    recoverScrollTop?.();
  }
}
