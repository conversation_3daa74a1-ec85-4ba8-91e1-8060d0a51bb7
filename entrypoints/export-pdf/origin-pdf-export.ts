import { Toast } from "@/pkg/lark/env";
import { confirmWithCancel, showCancelableProgress } from "@/pkg/utils/notification";
import { jsPDF } from "jspdf";

export async function exportOriginalPDF() {


  // 等待用户确认是否继续导出
  const shouldContinue = await confirmWithCancel("开始导出PDF？这可能需要一些时间。");
  if (!shouldContinue) {
    Toast.info({ content: "导出已取消", key: "exportOriginalPdf" });
    return;
  }

  const title = document
    .querySelector('meta[name="title"]')
    ?.getAttribute("content");

  // 找到 .pdf-viewport__list 的div
  const pdfList = document.querySelector(".pdf-viewport__list");

  if (!pdfList) {
    Toast.error({ content: "未找到PDF容器", key: "exportOriginalPdf" });
    return;
  }

  //span> data-sel="box-preview-pdf-toolbar-counter-count"
  const _totalPage = document.querySelector(
    'span[data-sel="box-preview-pdf-toolbar-counter-count"]'
  )?.textContent;
  const totalPage = parseInt(_totalPage!.split("/")[1]);
  console.error("总页数", totalPage);

  // 在开始下载前，先将PDF列表滚动到顶部
  Toast.loading({
    content: "正在准备下载，滚动到第一页",
    keepAlive: true,
    key: "exportOriginalPdf",
  });

  pdfList.scrollTop = 0;

  // 等待滚动完成和页面渲染
  setTimeout(() => {
    // 开始批量下载全部页面
    const maxPages = totalPage; // 下载全部页面
    console.error(`准备下载全部${maxPages}页`);
    startBatchDownload(title || "document", maxPages);
  }, 1000); // 等待1秒确保滚动到位
}

async function startBatchDownload(title: string, maxPages: number) {
  const nextButton = document.querySelector('li[data-key="next"]') as HTMLElement;
  const pdfList = document.querySelector(".pdf-viewport__list");

  if (!nextButton) {
    Toast.error({ content: "未找到翻页按钮", key: "exportOriginalPdf" });
    return;
  }

  if (!pdfList) {
    Toast.error({ content: "未找到PDF容器", key: "exportOriginalPdf" });
    return;
  }

  // 确保滚动到顶部，从第一页开始
  pdfList.scrollTop = 0;
  console.error("📍 确保从第一页开始下载");

  // 等待一下确保滚动完成
  await new Promise(resolve => setTimeout(resolve, 500));

  // 创建带取消按钮的进度提示
  const progressControl = showCancelableProgress(
    `正在准备收集PDF页面 (共${maxPages}页)`,
    "exportOriginalPdf"
  );

  // 创建PDF文档
  const pdf = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  let isFirstPage = true;
  const collectedImages: Array<{ pageNumber: number, imageData: string, width: number, height: number }> = [];

  // 收集所有页面的图片
  for (let currentPage = 1; currentPage <= maxPages; currentPage++) {
    // 检查是否已取消
    if (progressControl.isCancelled()) {
      console.error("❌ 用户取消导出，清理资源");
      // 清理已收集的图片数据
      collectedImages.length = 0;
      return;
    }

    try {
      // 更新进度
      progressControl.updateMessage(`正在收集第${currentPage}页 (${currentPage}/${maxPages})`);

      // 检查取消状态（在每个主要操作前）
      if (progressControl.isCancelled()) {
        console.error("❌ 用户取消导出，清理资源");
        collectedImages.length = 0;
        return;
      }

      // 如果是第一页，确保当前确实在第一页
      if (currentPage === 1) {
        // 检查当前页面是否真的是第一页
        const currentPageImg = await getCurrentPageImage(1);
        if (!currentPageImg) {
          console.error("🔄 第一页未找到，尝试滚动到顶部");
          pdfList.scrollTop = 0;
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // 获取当前页面的图片
      const currentImg = await getCurrentPageImage(currentPage);

      // 再次检查取消状态
      if (progressControl.isCancelled()) {
        console.error("❌ 用户取消导出，清理资源");
        collectedImages.length = 0;
        return;
      }

      if (currentImg) {
        // 使用Canvas + iframe的方式获取图片数据，避免权限问题
        const imageData = await convertImageToBase64WithCanvas(currentImg);

        // 转换完成后再次检查取消状态
        if (progressControl.isCancelled()) {
          console.error("❌ 用户取消导出，清理资源");
          collectedImages.length = 0;
          return;
        }

        collectedImages.push({
          pageNumber: currentPage,
          imageData: imageData,
          width: currentImg.naturalWidth,
          height: currentImg.naturalHeight
        });
        console.error(`✅ 第${currentPage}页收集成功`);
      } else {
        console.error(`❌ 第${currentPage}页未找到图片`);
      }

      // 如果不是最后一页，则翻到下一页
      if (currentPage < maxPages) {
        // 翻页前检查取消状态
        if (progressControl.isCancelled()) {
          console.error("❌ 用户取消导出，清理资源");
          collectedImages.length = 0;
          return;
        }

        await new Promise(resolve => setTimeout(resolve, 500)); // 等待500ms
        nextButton.click();
        await waitForPageLoad(currentPage + 1); // 等待下一页加载完成
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`第${currentPage}页收集失败:`, error);
      progressControl.close();
      Toast.error({
        content: `第${currentPage}页收集失败: ${errorMessage}`,
        key: "exportOriginalPdf",
      });
      // 清理资源
      collectedImages.length = 0;
      return;
    }
  }

  // 生成PDF
  if (collectedImages.length > 0) {
    // 检查是否在收集完成后被取消
    if (progressControl.isCancelled()) {
      console.error("❌ 用户取消导出，清理资源");
      collectedImages.length = 0;
      return;
    }

    progressControl.updateMessage(`正在生成PDF，共${collectedImages.length}页`);

    try {
      for (let i = 0; i < collectedImages.length; i++) {
        // 在每页处理前检查取消状态
        if (progressControl.isCancelled()) {
          console.error("❌ 用户取消导出，清理资源");
          collectedImages.length = 0;
          return;
        }

        const imageInfo = collectedImages[i];

        if (!isFirstPage) {
          pdf.addPage();
        }

        // 计算图片在A4页面中的适合尺寸
        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();
        const margin = 10; // 边距
        const maxWidth = pageWidth - 2 * margin;
        const maxHeight = pageHeight - 2 * margin;

        // 计算缩放比例以适应页面
        const scaleX = maxWidth / (imageInfo.width * 0.264583); // 转换像素到毫米
        const scaleY = maxHeight / (imageInfo.height * 0.264583);
        const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

        const imgWidth = imageInfo.width * 0.264583 * scale;
        const imgHeight = imageInfo.height * 0.264583 * scale;

        // 居中放置图片
        const x = (pageWidth - imgWidth) / 2;
        const y = (pageHeight - imgHeight) / 2;

        // 自动检测图片格式
        const imageFormat = imageInfo.imageData.indexOf('data:image/webp') === 0 ? 'WEBP' : 'JPEG';
        pdf.addImage(imageInfo.imageData, imageFormat, x, y, imgWidth, imgHeight);
        isFirstPage = false;

        // 更新生成进度
        progressControl.updateMessage(`正在生成PDF (${i + 1}/${collectedImages.length})`);

        console.error(`✅ 第${imageInfo.pageNumber}页已添加到PDF (格式: ${imageFormat})`);
      }

      // 最后检查一次取消状态
      if (progressControl.isCancelled()) {
        console.error("❌ 用户取消导出，清理资源");
        collectedImages.length = 0;
        return;
      }

      progressControl.updateMessage("正在保存PDF文件...");

      // 使用Canvas + iframe隐蔽下载方式保存PDF
      await savePDFWithIframe(pdf, `${title}_完整版.pdf`);

      // 关闭进度提示并显示成功消息
      progressControl.close();
      Toast.success({
        content: `PDF生成成功，共${collectedImages.length}页`,
        key: "exportOriginalPdf"
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error("PDF生成失败:", error);
      progressControl.close();
      Toast.error({
        content: `PDF生成失败: ${errorMessage}`,
        key: "exportOriginalPdf",
      });
      // 清理资源
      collectedImages.length = 0;
    }
  } else {
    progressControl.close();
    Toast.error({
      content: "未收集到任何页面图片",
      key: "exportOriginalPdf"
    });
  }
}

async function getCurrentPageImage(expectedPageNumber: number): Promise<HTMLImageElement | null> {
  const pdfList = document.querySelector(".pdf-viewport__list");
  if (!pdfList) return null;

  const liElements = pdfList.querySelectorAll("li");

  for (const li of liElements) {
    const img = li.querySelector("img");
    if (img && img.src) {
      // 查找div元素的data-sel属性来获取页数
      const pageContainer = li.querySelector('div[data-sel^="pdf-page-container-"]');

      if (pageContainer) {
        const dataSel = pageContainer.getAttribute('data-sel');
        if (dataSel) {
          const match = dataSel.match(/pdf-page-container-(\d+)/);
          if (match) {
            const pageNumber = parseInt(match[1], 10);
            if (pageNumber === expectedPageNumber) {
              return img;
            }
          }
        }
      }
    }
  }

  return null;
}

async function waitForPageLoad(expectedPageNumber: number): Promise<void> {
  return new Promise(async (resolve) => {
    let attempts = 0;
    const maxAttempts = 50; // 最多等待5秒

    const checkPage = async () => {
      attempts++;

      // 检查期望的页面是否已加载
      const img = await getCurrentPageImage(expectedPageNumber);

      if (img) {
        resolve();
        return;
      }

      if (attempts >= maxAttempts) {
        console.error(`等待第${expectedPageNumber}页加载超时`);
        resolve(); // 即使超时也继续，避免卡死
        return;
      }

      setTimeout(checkPage, 100); // 每100ms检查一次
    };

    checkPage();
  });
}

async function convertImageToBase64WithCanvas(img: HTMLImageElement): Promise<string> {
  return new Promise((resolve, reject) => {
    try {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d");

      // 图片压缩优化参数 - 可根据需要调整
      // maxWidth/maxHeight: 限制图片最大尺寸，减小文件大小
      // quality: 图片质量(0.1-1.0)，0.8为WebP的推荐值
      const maxWidth = 1200; // 最大宽度
      const maxHeight = 1600; // 最大高度
      const quality = 0.8; // 图片质量 80%

      let { width, height } = img;

      // 计算缩放比例
      const scaleX = maxWidth / width;
      const scaleY = maxHeight / height;
      const scale = Math.min(scaleX, scaleY, 1); // 只缩小，不放大

      const newWidth = Math.floor(width * scale);
      const newHeight = Math.floor(height * scale);

      canvas.width = newWidth;
      canvas.height = newHeight;

      if (ctx) {
        // 设置图像平滑处理
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // 检测浏览器是否支持WebP
        const supportsWebP = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;

        let dataURL: string;
        if (supportsWebP) {
          // 使用WebP格式 - 更小体积更好质量
          dataURL = canvas.toDataURL('image/webp', quality);
          console.error(`✅ 使用WebP格式压缩，质量: ${quality * 100}%`);
        } else {
          // 降级到JPEG格式
          dataURL = canvas.toDataURL('image/jpeg', quality * 0.9); // WebP不支持时稍微降低JPEG质量
          console.error(`⚠️ 浏览器不支持WebP，使用JPEG格式，质量: ${quality * 0.9 * 100}%`);
        }

        resolve(dataURL);
      } else {
        reject(new Error("无法获取canvas上下文"));
      }
    } catch (error) {
      reject(error);
    }
  });
}

async function savePDFWithIframe(pdf: jsPDF, filename: string): Promise<void> {
  return new Promise((resolve, reject) => {
    try {
      // 生成PDF的blob数据
      const pdfBlob = pdf.output('blob');

      // 创建隐藏的iframe来下载
      const iframe = document.createElement("iframe");
      iframe.style.display = "none";
      document.body.appendChild(iframe);

      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
      if (iframeDoc) {
        const url = URL.createObjectURL(pdfBlob);
        const link = iframeDoc.createElement("a");
        link.href = url;
        link.download = filename;

        // 模拟用户点击事件
        const event = new MouseEvent("click", {
          bubbles: true,
          cancelable: true,
          view: window,
        });

        iframeDoc.body.appendChild(link);
        link.dispatchEvent(event);

        // 延迟清理
        setTimeout(() => {
          URL.revokeObjectURL(url);
          if (iframe.parentNode) {
            document.body.removeChild(iframe);
          }
        }, 1000);

        console.error("✅ PDF下载成功 - 使用方式: Canvas + iframe 隐蔽下载");
        resolve();
      } else {
        reject(new Error("无法访问iframe文档"));
      }
    } catch (error) {
      reject(error);
    }
  });
}
