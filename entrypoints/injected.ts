import { exportMdInjected } from "./export-md/md-util";
import { exportPdfInjected, } from "./export-pdf/pdf-util";
import { exportWordInjected } from "./export-word/word-util";

export default defineUnlistedScript(async () => {
  if (window.exportConfig) {
    const { type } = window.exportConfig;
    if (type === 'single') {
      // 单个文档导出逻辑
      await exportPdfInjected()
    } else if (type === 'word') {
      // word导出逻辑
      await exportWordInjected()
    } else if (type === 'md') {
      // md导出逻辑
      await exportMdInjected()
    }
  }
});