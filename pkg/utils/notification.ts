import { Minute } from "../common"
import { Toast } from "../lark"


export const confirm = (): Promise<boolean> => {
  return new Promise<boolean>(resolve => {
    let confirmed = false

    Toast.info({
      closable: true,
      duration: Minute,
      content: 'continue',
      actionText: 'confirm',
      onActionClick: () => {
        confirmed = true
      },
      onClose: () => {
        resolve(confirmed)
      },
    })
  })
}

export const confirmWithCancel = (message: string = '是否继续导出？'): Promise<boolean> => {
  return new Promise<boolean>(resolve => {
    let confirmed = false

    Toast.info({
      closable: true,
      duration: Minute,
      content: message,
      actionText: '继续导出',
      onActionClick: () => {
        confirmed = true
      },
      onClose: () => {
        resolve(confirmed)
      },
    })
  })
}

// 显示带取消按钮的进度提示，返回一个可以更新内容和检查是否取消的对象
export const showCancelableProgress = (initialMessage: string, key: string = 'cancelableProgress') => {
  let isCancelled = false;
  
  Toast.loading({
    content: initialMessage,
    keepAlive: true,
    key: key,
    actionText: '取消导出',
    onActionClick: () => {
      isCancelled = true;
      Toast.remove(key);
    },
  });

  return {
    updateMessage: (message: string) => {
      if (!isCancelled) {
        Toast.loading({
          content: message,
          keepAlive: true,
          key: key,
          actionText: '取消导出',
          onActionClick: () => {
            isCancelled = true;
            Toast.remove(key);
          },
        });
      }
    },
    isCancelled: () => isCancelled,
    close: () => {
      Toast.remove(key);
    }
  };
};
