import MarkdownIt from "markdown-it";


// 创建 markdown-it 实例，启用常用插件
const md = new MarkdownIt({
  html: true,        // 启用 HTML 标签
  breaks: true,      // 将换行符转换为 <br>
  linkify: true,     // 自动检测链接
  typographer: true, // 启用一些语言中性的替换 + 引号美化
});

/**
 * 基于原始 Markdown 内容分析图片分组
 * 更准确地处理空行分隔的图片组
 */
function analyzeImageGroups(markdown: string): string[] {
  const lines = markdown.split('\n');
  const imageLineIndices: number[] = [];

  // 先找出所有图片行的索引
  for (let i = 0; i < lines.length; i++) {
    const trimmedLine = lines[i].trim();
    if (trimmedLine.match(/^!\[.*\]\(.*\)$/)) {
      imageLineIndices.push(i);
    }
  }

  if (imageLineIndices.length === 0) {
    return [];
  }

  // 基于图片行索引分组
  const groups: string[] = [];
  let currentGroup: string[] = [];

  for (let i = 0; i < imageLineIndices.length; i++) {
    const currentIndex = imageLineIndices[i];
    const currentImageLine = lines[currentIndex].trim();

    currentGroup.push(currentImageLine);

    // 检查当前图片后面是否应该分组
    let shouldEndGroup = false;

    if (i < imageLineIndices.length - 1) {
      // 不是最后一个图片，检查到下一个图片之间的内容
      const nextImageIndex = imageLineIndices[i + 1];

      let emptyLineCount = 0;
      let hasNonEmptyContent = false;

      for (let j = currentIndex + 1; j < nextImageIndex; j++) {
        const line = lines[j];
        const trimmed = line.trim();

        if (trimmed === '') {
          emptyLineCount++;
        } else {
          hasNonEmptyContent = true;
        }
      }

      // 判断是否应该分组：
      // 1. 有非空内容 → 分组
      // 2. 有多个连续空行 → 分组  
      // 3. 只有一个空行 → 不分组（继续合并）
      if (hasNonEmptyContent || emptyLineCount > 1) {
        shouldEndGroup = true;
      }
    } else {
      // 最后一个图片，结束当前组
      shouldEndGroup = true;
    }

    if (shouldEndGroup) {
      groups.push(currentGroup.join('\n'));
      currentGroup = [];
    }
  }

  return groups;
}

/**
 * 使用 markdown-it 将 Markdown 字符串转换为 HTML
 */
export function markdownToHtml(markdown: string): string {
  if (!markdown) return '';

  // 分析原始 markdown 中的图片分组
  const imageGroups = analyzeImageGroups(markdown);

  // 先正常转换 Markdown 为 HTML
  let htmlContent = md.render(markdown);

  // 如果有多图片组，处理连续图片
  const hasMultiImageGroups = imageGroups.some(group =>
    group.split('\n').filter(line => line.trim()).length > 1
  );

  if (hasMultiImageGroups) {
    // 直接匹配连续的 <p><img></p> 标签模式
    // 使用简单的正则表达式匹配连续的图片段落
    htmlContent = htmlContent.replace(
      /(<p><img[^>]*><\/p>)(\s*<p><img[^>]*><\/p>)+/g,
      (match) => {
        // 提取所有img标签
        const imgTags = match.match(/<img[^>]*>/g) || [];
        return `<div class="consecutive-images">${imgTags.join(' ')}</div>`;
      }
    );
  }

  // 处理剩余的单独图片（去掉p标签包裹）
  htmlContent = htmlContent.replace(/<p>(<img[^>]*>)<\/p>/g, '$1');

  return htmlContent;
}

