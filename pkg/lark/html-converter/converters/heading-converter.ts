import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class HeadingConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return [
      BlockType.HEADING1,
      BlockType.HEADING2,
      BlockType.HEADING3,
      BlockType.HEADING4,
      BlockType.HEADING5,
      BlockType.HEADING6,
    ].includes(blockType)
  }

  convert(block: Blocks, context: ConversionContext): string {
    const level = this.getHeadingLevel(block.type)
    const ops = block.zoneState?.content.ops || []
    let content = this.convertOperationsToHtml(ops, context)

    // 处理自动编号
    if ('snapshot' in block && block.snapshot && 'seq' in block.snapshot) {
      const snapshot = block.snapshot as any
      if (typeof snapshot.seq === 'string') {
        const sequenceNumber = this.generateSequenceNumber(snapshot, block.type)
        if (sequenceNumber) {
          content = `${sequenceNumber} ${content}`
        }
      }
    }

    const className = this.getClassName(`heading-${level}`, context)
    const customStyle = this.getCustomStyle(`heading${level}`, context)

    if (context.options.useInlineStyles !== false) {
      const style = customStyle ? ` style="${customStyle}"` : ''
      return `<h${level}${style}>${content}</h${level}>`
    } else {
      return `<h${level} class="${className}">${content}</h${level}>`
    }
  }

  private getHeadingLevel(blockType: BlockType): number {
    switch (blockType) {
      case BlockType.HEADING1: return 1
      case BlockType.HEADING2: return 2
      case BlockType.HEADING3: return 3
      case BlockType.HEADING4: return 4
      case BlockType.HEADING5: return 5
      case BlockType.HEADING6: return 6
      default: return 1
    }
  }

  private generateSequenceNumber(snapshot: any, blockType: BlockType): string {
    if (snapshot.seq === 'auto') {
      // 这里可以实现自动编号逻辑，暂时返回空
      return ''
    }

    if (snapshot.seq_level === 'auto') {
      // 实现多级编号，暂时简化处理
      return `${snapshot.seq}.`
    }

    return `${snapshot.seq}.`
  }
} 