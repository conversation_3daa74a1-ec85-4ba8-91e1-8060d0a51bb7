import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class IframeConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.IFRAME
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.IFRAME) return ''

    const iframeData = block.snapshot?.iframe
    if (!iframeData) return ''

    const url = iframeData.component?.url || ''
    const height = iframeData.height || 400

    if (!url) {
      return this.createPlaceholder('无效的内嵌链接', context)
    }

    // 安全检查：确保URL是安全的
    if (!this.isValidUrl(url)) {
      return this.createPlaceholder('不安全的内嵌链接', context)
    }

    const className = this.getClassName('iframe', context)
    const customStyle = this.getCustomStyle('iframe', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = `width: 100%; height: ${height}px; border: 1px solid #d1d9e0; border-radius: 6px; margin: 16px 0;`
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<iframe src="${this.escapeHtml(url)}" frameborder="0" allowfullscreen${style}></iframe>`
    } else {
      return `<iframe src="${this.escapeHtml(url)}" frameborder="0" allowfullscreen class="${className}" height="${height}"></iframe>`
    }
  }

  private createPlaceholder(message: string, context: ConversionContext): string {
    const className = this.getClassName('iframe-placeholder', context)

    if (context.options.useInlineStyles !== false) {
      const style = 'width: 100%; height: 200px; border: 2px dashed #d1d9e0; border-radius: 6px; margin: 16px 0; display: flex; align-items: center; justify-content: center; background: #f6f8fa; color: #656d76;'

      return `<div style="${style}">` +
        `<div style="text-align: center;">` +
        `<div style="font-size: 24px; margin-bottom: 8px;">🔗</div>` +
        `<div>${this.escapeHtml(message)}</div>` +
        `</div>` +
        `</div>`
    } else {
      return `<div class="${className}">` +
        `<div class="placeholder-content">` +
        `<div class="placeholder-icon">🔗</div>` +
        `<div class="placeholder-message">${this.escapeHtml(message)}</div>` +
        `</div>` +
        `</div>`
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)

      // 只允许 http 和 https 协议
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return false
      }

      // 可以添加更多的安全检查，比如域名白名单等
      return true
    } catch {
      return false
    }
  }
} 