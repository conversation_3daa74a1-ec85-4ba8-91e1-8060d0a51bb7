import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class CodeConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.CODE
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.CODE) return ''

    const language = block.language || ''
    const code = block.zoneState?.allText || ''

    const className = this.getClassName('code', context)
    const customStyle = this.getCustomStyle('code', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'background: #f6f8fa; border: 1px solid #d1d9e0; border-radius: 6px; padding: 16px; margin: 16px 0; overflow: auto; font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace; font-size: 14px; line-height: 1.45;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      if (language) {
        return `<pre${style}><code class="language-${this.escapeHtml(language)}">${this.escapeHtml(code)}</code></pre>`
      } else {
        return `<pre${style}><code>${this.escapeHtml(code)}</code></pre>`
      }
    } else {
      if (language) {
        return `<pre class="${className}"><code class="language-${this.escapeHtml(language)}">${this.escapeHtml(code)}</code></pre>`
      } else {
        return `<pre class="${className}"><code>${this.escapeHtml(code)}</code></pre>`
      }
    }
  }
} 