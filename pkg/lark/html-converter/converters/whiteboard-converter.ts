import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class WhiteboardConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.WHITEBOARD
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.WHITEBOARD) return ''

    const caption = block.snapshot?.caption
    const alt = this.extractAltText(caption) || '白板'

    // 创建白板图片对象，模拟markdown AST中的image结构
    const whiteboardObj = {
      type: 'image',
      url: '', // 初始为空，后续会被设置为data URL
      alt,
      data: {
        fetchBlob: () => this.fetchWhiteboardBlob(block)
      }
    }

    // 如果启用了图片转换，添加到上下文中供后续处理
    if (context.options.convertImages !== false) {
      const imgElement = document.createElement('img')
      imgElement.alt = alt
      imgElement.dataset.type = 'whiteboard'
      if (block.record?.id) {
        imgElement.dataset.recordId = block.record.id
      }

      // 异步获取白板数据并转换为data URL
      this.processWhiteboardAsync(whiteboardObj).then(dataUrl => {
        if (dataUrl) {
          imgElement.src = dataUrl
          whiteboardObj.url = dataUrl

          // 更新所有相同recordId的白板元素
          const recordId = block.record?.id
          if (recordId) {
            const allWhiteboardsWithRecordId = document.querySelectorAll(`[data-record-id="${recordId}"]`)
            allWhiteboardsWithRecordId.forEach(element => {
              if (element instanceof HTMLImageElement) {
                element.src = dataUrl
              } else if (element instanceof HTMLDivElement) {
                // 如果是div形式的白板占位符，可以替换为img元素
                const img = document.createElement('img')
                img.src = dataUrl
                img.alt = alt
                img.style.cssText = 'max-width: 100%; height: auto; margin: 16px 0; border-radius: 4px;'
                element.parentNode?.replaceChild(img, element)
              }
            })
          }
        }
      }).catch(error => {
        console.error('白板处理失败:', error)
      })

      context.images.push(imgElement)
    }

    const className = this.getClassName('whiteboard', context)
    const customStyle = this.getCustomStyle('whiteboard', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'max-width: 100%; height: auto; margin: 16px 0; border: 2px dashed #d1d9e0; border-radius: 8px; background: #f6f8fa; min-height: 200px; display: flex; align-items: center; justify-content: center; color: #656d76; font-size: 14px;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<div data-type="whiteboard" data-record-id="${this.escapeHtml(block.record?.id || '')}"${style}>` +
        `<div style="text-align: center;">` +
        `<div style="font-size: 24px; margin-bottom: 8px;">🎨</div>` +
        `<div>白板: ${this.escapeHtml(alt)}</div>` +
        `<div style="font-size: 12px; color: #8c959f; margin-top: 4px;">正在加载白板内容...</div>` +
        `</div>` +
        `</div>`
    } else {
      return `<div class="${className}" data-type="whiteboard" data-record-id="${this.escapeHtml(block.record?.id || '')}">` +
        `<div class="whiteboard-placeholder">` +
        `<div class="whiteboard-icon">🎨</div>` +
        `<div class="whiteboard-title">白板: ${this.escapeHtml(alt)}</div>` +
        `<div class="whiteboard-hint">正在加载白板内容...</div>` +
        `</div>` +
        `</div>`
    }
  }

  private async fetchWhiteboardBlob(block: Blocks): Promise<Blob | null> {
    if (block.type !== BlockType.WHITEBOARD) return null

    try {
      const whiteboardBlock = block.whiteboardBlock
      if (!whiteboardBlock?.isolateEnv) return null

      const { hasRatioApp, getRatioApp } = whiteboardBlock.isolateEnv

      if (!hasRatioApp()) return null

      const ratioApp = getRatioApp()
      const imageDataWrapper = await ratioApp.ratioAppProxy.getOriginImageDataByNodeId(24, [''], false, 2)

      if (!imageDataWrapper) return null

      // 转换ImageData为Blob
      const { imageDataToBlob } = await import('@/pkg/common/image')
      const blob = await imageDataToBlob(imageDataWrapper.data, {
        onDispose: imageDataWrapper.release
      })

      return blob
    } catch (error) {
      console.error('获取白板数据失败:', error)
      return null
    }
  }

  private async processWhiteboardAsync(whiteboardObj: any): Promise<string | null> {
    try {
      if (!whiteboardObj.data?.fetchBlob) return null

      // 获取白板blob数据
      const blob = await whiteboardObj.data.fetchBlob()
      if (!blob) return null

      // 转换为data URL
      const dataUrl = await this.blobToDataUrl(blob)

      return dataUrl
    } catch (error) {
      console.error('处理白板失败:', error)
      return null
    }
  }

  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  private extractAltText(caption?: any): string {
    if (!caption) return ''

    try {
      // 尝试从caption中提取文本
      const text = caption.text?.initialAttributedTexts?.text?.[0]
      return typeof text === 'string' ? text.trim() : ''
    } catch {
      return ''
    }
  }
} 