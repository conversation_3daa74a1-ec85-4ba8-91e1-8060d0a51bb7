import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class TextConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.TEXT ||
      blockType === BlockType.HEADING7 ||
      blockType === BlockType.HEADING8 ||
      blockType === BlockType.HEADING9
  }

  convert(block: Blocks, context: ConversionContext): string {
    const ops = block.zoneState?.content.ops || []
    const content = this.convertOperationsToHtml(ops, context)

    if (!content.trim()) {
      return '<div></div>'
    }

    const className = this.getClassName('text', context)
    const customStyle = this.getCustomStyle('text', context)

    if (context.options.useInlineStyles !== false) {
      const style = customStyle ? ` style="${customStyle}"` : ''
      return `<div${style}>${content}</div>`
    } else {
      return `<div class="${className}">${content}</div>`
    }
  }
} 