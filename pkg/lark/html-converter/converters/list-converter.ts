import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class ListConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.BULLET ||
      blockType === BlockType.ORDERED ||
      blockType === BlockType.TODO
  }

  convert(block: Blocks, context: ConversionContext): string {
    // 获取列表项内容
    const ops = block.zoneState?.content?.ops || []
    const content = this.convertOperationsToHtml(ops, context)

    // 处理子块（列表项可能包含嵌套的列表或其他类型的块）
    const childrenHtml: string[] = []
    if (block.children && block.children.length > 0) {
      // 使用基类的分组方法，更好地处理混合内容
      const groupedChildren = this.groupConsecutiveListBlocks(block.children)
      
      for (const group of groupedChildren) {
        if (group.type === 'list' && group.listType) {
          // 处理嵌套列表
          const listHtml = this.convertNestedListGroup(group.blocks, group.listType, context)
          if (listHtml) {
            childrenHtml.push(listHtml)
          }
        } else {
          // 处理非列表子块（如文本、图片等）
          for (const child of group.blocks) {
            const childHtml = this.convertChildBlock(child, context)
            if (childHtml && childHtml.trim()) {
              childrenHtml.push(childHtml)
            }
          }
        }
      }
    }

    const fullContent = content + (childrenHtml.length > 0 ? '\n' + childrenHtml.join('\n') : '')

    if (block.type === BlockType.TODO) {
      return this.convertTodoItem(block, fullContent, context)
    } else if (block.type === BlockType.ORDERED) {
      return this.convertOrderedItem(block, fullContent, context)
    } else {
      return this.convertBulletItem(fullContent, context)
    }
  }

  private convertTodoItem(block: Blocks, content: string, context: ConversionContext): string {
    if (block.type !== BlockType.TODO) return ''

    const isChecked = block.snapshot && 'done' in block.snapshot ? Boolean(block.snapshot.done) : false
    const className = this.getClassName('todo', context)
    const customStyle = this.getCustomStyle('todo', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'list-style: none; margin: 4px 0; display: flex; align-items: flex-start;'
      const checkboxStyle = 'margin-right: 8px; margin-top: 2px;'
      const contentStyle = isChecked ? 'text-decoration: line-through; color: #888;' : ''

      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<li${style}>` +
        `<input type="checkbox"${isChecked ? ' checked' : ''} disabled style="${checkboxStyle}">` +
        `<span style="${contentStyle}">${content || ''}</span>` +
        `</li>`
    } else {
      return `<li class="${className}">` +
        `<input type="checkbox"${isChecked ? ' checked' : ''} disabled>` +
        `<span${isChecked ? ' class="checked"' : ''}>${content || ''}</span>` +
        `</li>`
    }
  }

  private convertOrderedItem(block: Blocks, content: string, context: ConversionContext): string {
    const className = this.getClassName('ordered', context)
    const customStyle = this.getCustomStyle('ordered', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'margin: 4px 0;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`
      return `<li${style}>${content || ''}</li>`
    } else {
      return `<li class="${className}">${content || ''}</li>`
    }
  }

  private convertBulletItem(content: string, context: ConversionContext): string {
    const className = this.getClassName('bullet', context)
    const customStyle = this.getCustomStyle('bullet', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'margin: 4px 0;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`
      return `<li${style}>${content || ''}</li>`
    } else {
      return `<li class="${className}">${content || ''}</li>`
    }
  }

  /**
   * 转换嵌套列表组
   */
  private convertNestedListGroup(listBlocks: Blocks[], listType: BlockType, context: ConversionContext): string {
    if (!listBlocks.length) return ''

    const listItems = listBlocks.map(block => this.convert(block, context)).filter(Boolean)
    if (!listItems.length) return ''

    const listClassName = this.getClassName(
      listType === BlockType.TODO ? 'todo-list' :
        listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list',
      context
    )

    if (context.options.useInlineStyles !== false) {
      if (listType === BlockType.TODO) {
        const style = 'list-style: none; padding-left: 0;'
        return `<ul style="${style}">${listItems.join('')}</ul>`
      } else if (listType === BlockType.ORDERED) {
        const style = 'padding-left: 24px; margin: 0;'
        const markerStyle = 'li::marker { color: #0084ff; }'
        return `<style>${markerStyle}</style><ol style="${style}">${listItems.join('')}</ol>`
      } else {
        const style = 'padding-left: 24px; margin: 0;'
        const markerStyle = 'li::marker { color: #0084ff; }'
        return `<style>${markerStyle}</style><ul style="${style}">${listItems.join('')}</ul>`
      }
    } else {
      if (listType === BlockType.TODO) {
        return `<ul class="${listClassName}">${listItems.join('')}</ul>`
      } else if (listType === BlockType.ORDERED) {
        return `<ol class="${listClassName}">${listItems.join('')}</ol>`
      } else {
        return `<ul class="${listClassName}">${listItems.join('')}</ul>`
      }
    }
  }
} 