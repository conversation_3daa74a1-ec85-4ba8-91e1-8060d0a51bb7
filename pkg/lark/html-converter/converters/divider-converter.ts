import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class DividerConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.DIVIDER
  }

  convert(block: Blocks, context: ConversionContext): string {
    const className = this.getClassName('divider', context)
    const customStyle = this.getCustomStyle('divider', context)

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'border: none; border-top: 1px solid #ccc; margin: 16px 0;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`
      return `<hr${style}>`
    } else {
      return `<hr class="${className}">`
    }
  }
} 