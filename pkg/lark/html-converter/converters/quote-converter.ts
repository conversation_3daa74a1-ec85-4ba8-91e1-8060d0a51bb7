import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class QuoteConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.QUOTE_CONTAINER || blockType === BlockType.CALLOUT
  }

  convert(block: Blocks, context: ConversionContext): string {
    // 使用基类提供的通用子块处理方法
    const childrenHtml = this.processChildren(block.children || [], context)

    // 如果没有子块内容，尝试从当前块提取文本
    if (childrenHtml.length === 0) {
      const ops = block.zoneState?.content?.ops || []
      const content = this.convertOperationsToHtml(ops, context)
      if (content.trim()) {
        childrenHtml.push(`<div>${content}</div>`)
      }
    }

    const content = childrenHtml.join('\n')
    if (!content.trim()) {
      return '<blockquote><div></div></blockquote>'
    }

    const className = this.getClassName(block.type === BlockType.CALLOUT ? 'callout' : 'quote', context)
    const customStyle = this.getCustomStyle(block.type === BlockType.CALLOUT ? 'callout' : 'quote', context)

    if (context.options.useInlineStyles !== false) {
      const finalStyle = this.buildInlineStyles(block, customStyle)
      
      // 为callout添加图标
      if (block.type === BlockType.CALLOUT) {
        const iconHtml = this.buildCalloutIcon(block)
        if (iconHtml) {
          const contentStyle = 'flex: 1; min-width: 0;'
          return `<blockquote style="${finalStyle}">${iconHtml}<div style="${contentStyle}">${content}</div></blockquote>`
        }
      }
      
      return `<blockquote style="${finalStyle}">${content}</blockquote>`
    } else {
      return `<blockquote class="${className}">${content}</blockquote>`
    }
  }

  /**
   * 处理子块，支持列表分组和任意类型的子块
   */
  private processChildren(children: Blocks[], context: ConversionContext): string[] {
    if (!children || children.length === 0) {
      return []
    }

    const childrenHtml: string[] = []
    const groupedChildren = this.groupConsecutiveListBlocks(children)
    
    for (const group of groupedChildren) {
      if (group.type === 'list' && group.listType) {
        // 处理列表组
        const listHtml = this.convertListGroup(group.blocks, group.listType, context)
        if (listHtml) {
          childrenHtml.push(listHtml)
        }
      } else {
        // 处理单个非列表块
        for (const child of group.blocks) {
          const childHtml = this.convertChildBlock(child, context)
          if (childHtml && childHtml.trim()) {
            childrenHtml.push(childHtml)
          }
        }
      }
    }

    return childrenHtml
  }

  /**
   * 构建内联样式
   */
  private buildInlineStyles(block: Blocks, customStyle?: string): string {
    const snapshot = block.snapshot as any || {}
    const backgroundColor = snapshot.background_color || ''
    const borderColor = snapshot.border_color || ''
    const textColor = snapshot.text_color || ''
    const align = snapshot.align || 'left'
    
    let styleComponents: string[] = []
    
    if (block.type === BlockType.CALLOUT) {
      // Callout样式 - 左右布局
      styleComponents.push('padding: 16px')
      styleComponents.push('margin: 16px 0')
      styleComponents.push('border-radius: 8px')
      styleComponents.push('display: flex')
      styleComponents.push('align-items: flex-start')
      styleComponents.push('gap: 12px')
      
      if (backgroundColor) {
        styleComponents.push(`background-color: ${backgroundColor}`)
      } else {
        styleComponents.push('background-color: #f0f8ff')
      }
      
      if (borderColor) {
        styleComponents.push(`border: 1px solid ${borderColor}`)
      } else {
        styleComponents.push('border: 1px solid #0084ff')
      }
    } else {
      // Quote样式
      styleComponents.push('padding: 16px')
      styleComponents.push('margin: 16px 0')
      styleComponents.push('font-style: italic')
      
      if (backgroundColor) {
        styleComponents.push(`background-color: ${backgroundColor}`)
      }
      
      if (borderColor) {
        styleComponents.push(`border-left: 4px solid ${borderColor}`)
      } else {
        styleComponents.push('border-left: 4px solid #ddd')
      }
      
      if (!textColor) {
        styleComponents.push('color: #666')
      }
    }
    
    // 应用文本颜色和对齐方式
    if (textColor) {
      styleComponents.push(`color: ${textColor}`)
    }
    styleComponents.push(`text-align: ${align}`)
    
    return customStyle ? `${styleComponents.join('; ')}; ${customStyle}` : styleComponents.join('; ')
  }

  /**
   * 构建Callout图标
   */
  private buildCalloutIcon(block: Blocks): string {
    const snapshot = block.snapshot as any || {}
    const emojiId = snapshot.emoji_id || ''
    
    if (!emojiId) return ''
    
    const emoji = this.getEmojiFromId(emojiId)
    const iconStyle = 'font-size: 20px; line-height: 1; flex-shrink: 0; margin-top: 6px;'
    return `<span style="${iconStyle}">${emoji}</span>`
  }

  private getEmojiFromId(emojiId: string): string {
    // 映射emoji_id到实际的emoji字符
    const emojiMap: Record<string, string> = {
      'round_pushpin': '📍',
      'pushpin': '📌',
      'warning': '⚠️',
      'exclamation': '❗',
      'question': '❓',
      'info': 'ℹ️',
      'bulb': '💡',
      'star': '⭐',
      'heart': '❤️',
      'fire': '🔥',
      'check': '✅',
      'cross': '❌',
      'plus': '➕',
      'minus': '➖',
      'arrow_right': '➡️',
      'arrow_left': '⬅️',
      'arrow_up': '⬆️',
      'arrow_down': '⬇️',
      'book': '📚',
      'memo': '📝',
      'gear': '⚙️',
      'bell': '🔔',
      'key': '🔑',
      'lock': '🔒',
      'unlock': '🔓',
    }
    
    return emojiMap[emojiId] || '📍' // 默认使用圆形图钉
  }
} 