import { BlockConverter, ConversionContext } from '../base-converter'
import { BlockType, Blocks } from '../../docx'

export class FileConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === BlockType.FILE
  }

  convert(block: Blocks, context: ConversionContext): string {
    if (block.type !== BlockType.FILE) return ''

    const fileData = block.snapshot?.file
    if (!fileData) return ''

    const { name, token } = fileData

    // 如果启用了文件转换，添加到上下文中供后续处理
    if (context.options.convertFiles !== false) {
      const linkElement = document.createElement('a')
      linkElement.textContent = name
      linkElement.dataset.token = token
      linkElement.dataset.name = name
      if (block.record?.id) {
        linkElement.dataset.recordId = block.record.id
      }
      context.files.push(linkElement)
    }

    const className = this.getClassName('file', context)
    const customStyle = this.getCustomStyle('file', context)

    // 根据文件扩展名确定图标
    const fileIcon = this.getFileIcon(name)
    const fileSize = this.getFileSizeDisplay(name) // 这里可以扩展显示文件大小

    if (context.options.useInlineStyles !== false) {
      const defaultStyle = 'display: inline-flex; align-items: center; padding: 8px 12px; border: 1px solid #d1d9e0; border-radius: 6px; background: #f6f8fa; text-decoration: none; color: #0969da; margin: 8px 0; transition: background-color 0.2s;'
      const iconStyle = 'width: 16px; height: 16px; margin-right: 8px;'
      const style = customStyle ? ` style="${customStyle}"` : ` style="${defaultStyle}"`

      return `<a href="#" data-token="${this.escapeHtml(token)}" data-name="${this.escapeHtml(name)}" data-record-id="${this.escapeHtml(block.record?.id || '')}"${style}>` +
        `<span style="${iconStyle}">${fileIcon}</span>` +
        `<span>${this.escapeHtml(name)}</span>` +
        `</a>`
    } else {
      return `<a href="#" class="${className}" data-token="${this.escapeHtml(token)}" data-name="${this.escapeHtml(name)}" data-record-id="${this.escapeHtml(block.record?.id || '')}">` +
        `<span class="file-icon">${fileIcon}</span>` +
        `<span class="file-name">${this.escapeHtml(name)}</span>` +
        `</a>`
    }
  }

  private getFileIcon(fileName: string): string {
    const extension = fileName.split('.').pop()?.toLowerCase() || ''

    // 根据文件扩展名返回对应的图标（使用Unicode字符或SVG）
    switch (extension) {
      case 'pdf':
        return '📄'
      case 'doc':
      case 'docx':
        return '📝'
      case 'xls':
      case 'xlsx':
        return '📊'
      case 'ppt':
      case 'pptx':
        return '📑'
      case 'zip':
      case 'rar':
      case '7z':
        return '🗜️'
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return '🖼️'
      case 'mp3':
      case 'wav':
      case 'flac':
        return '🎵'
      case 'mp4':
      case 'avi':
      case 'mov':
        return '🎬'
      case 'txt':
        return '📄'
      default:
        return '📎'
    }
  }

  private getFileSizeDisplay(fileName: string): string {
    // 这里可以扩展来显示实际的文件大小
    // 目前返回空字符串，因为文件大小信息可能不在当前的数据结构中
    return ''
  }
} 