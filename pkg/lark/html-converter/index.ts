import { PageBlock } from '../docx'
import { HtmlTransformer } from './html-transformer'
import { HtmlTransformerOptions, HtmlTransformResult } from './base-converter'

// 导出主要类型和类
export { HtmlTransformer } from './html-transformer'
export { BlockConverter } from './base-converter'
export type {
  HtmlTransformerOptions,
  HtmlTransformResult,
  ConversionContext,
  HtmlAttributes,
  TocItem
} from './base-converter'

// 导出所有转换器，方便用户扩展
export * from './converters'

// 创建默认实例
const defaultTransformer = new HtmlTransformer()

/**
 * 将 docx.rootBlock 转换为 HTML
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果
 * 
 * @example
 * ```typescript
 * import { convertDocxToHtml } from 'pkg/lark/html-converter'
 * import { docx } from 'pkg/lark/docx'
 * 
 * const result = convertDocxToHtml(docx.rootBlock, {
 *   useInlineStyles: true,
 *   cssClassPrefix: 'my-prefix'
 * })
 * 
 * console.error(result.html)
 * console.error(result.styles) // CSS样式（如果useInlineStyles为false）
 * ```
 */
export function convertDocxToHtml(
  rootBlock: PageBlock | null,
  options: HtmlTransformerOptions = {}
): HtmlTransformResult {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  return defaultTransformer.transform(rootBlock, options)
}

/**
 * 将 docx.rootBlock 转换为 HTML，并预处理图片为 data URL
 * 
 * @param rootBlock 飞书文档的根块
 * @param options 转换选项
 * @returns HTML转换结果（图片已转换为data URL）
 * 
 * @example
 * ```typescript
 * import { convertDocxToHtmlWithImages } from 'pkg/lark/html-converter'
 * import { docx } from 'pkg/lark/docx'
 * 
 * const result = await convertDocxToHtmlWithImages(docx.rootBlock, {
 *   useInlineStyles: true,
 *   cssClassPrefix: 'my-prefix'
 * })
 * 
 * console.error(result.html) // 图片已转换为data URL
 * ```
 */
export async function convertDocxToHtmlWithImages(
  rootBlock: PageBlock | null,
  options: HtmlTransformerOptions = {}
): Promise<HtmlTransformResult> {
  if (!rootBlock) {
    return {
      html: '',
      images: [],
      files: [],
      styles: undefined,
      toc: [],
      htmlWithToc: ''
    }
  }

  // 使用transformWithImages方法，等待图片处理完成
  return await defaultTransformer.transformWithImages(rootBlock, options)
}

/**
 * 处理HTML中的图片，等待它们转换为data URL
 * 
 * @param html HTML字符串
 * @param timeout 超时时间（毫秒），默认30秒
 */
async function processImagesInHtml(html: string, timeout: number = 30000): Promise<void> {
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  tempDiv.style.cssText = 'position: absolute; top: -9999px; left: -9999px; visibility: hidden;'
  document.body.appendChild(tempDiv)

  try {
    // 查找所有需要处理的图片
    const images = tempDiv.querySelectorAll('img[data-token]') as NodeListOf<HTMLImageElement>
    const whiteboards = tempDiv.querySelectorAll('[data-type="whiteboard"]') as NodeListOf<HTMLElement>

    if (images.length === 0 && whiteboards.length === 0) {
      return
    }

    console.error(`开始处理 ${images.length} 张图片和 ${whiteboards.length} 个白板`)

    // 创建Promise数组来等待所有图片加载
    const promises: Promise<void>[] = []

    // 处理普通图片
    images.forEach((img, index) => {
      const promise = new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          if (img.src && img.src !== '' && img.src.startsWith('data:')) {
            clearInterval(checkInterval)
            console.error(`图片 ${index + 1}/${images.length} 处理完成`)
            resolve()
          }
        }, 100)

        // 设置超时
        setTimeout(() => {
          clearInterval(checkInterval)
          console.warn(`图片 ${index + 1}/${images.length} 处理超时`)
          resolve()
        }, timeout)
      })
      promises.push(promise)
    })

    // 处理白板
    whiteboards.forEach((whiteboard, index) => {
      const promise = new Promise<void>((resolve) => {
        const checkInterval = setInterval(() => {
          const img = whiteboard.querySelector('img')
          if (img && img.src && img.src.startsWith('data:')) {
            clearInterval(checkInterval)
            console.error(`白板 ${index + 1}/${whiteboards.length} 处理完成`)
            resolve()
          }
        }, 100)

        // 设置超时
        setTimeout(() => {
          clearInterval(checkInterval)
          console.warn(`白板 ${index + 1}/${whiteboards.length} 处理超时`)
          resolve()
        }, timeout)
      })
      promises.push(promise)
    })

    // 等待所有图片处理完成
    await Promise.all(promises)
    console.error('所有图片和白板处理完成')

  } finally {
    // 清理临时元素
    if (tempDiv.parentNode) {
      document.body.removeChild(tempDiv)
    }
  }
}

/**
 * 创建新的HTML转换器实例
 * 
 * @returns 新的转换器实例
 * 
 * @example
 * ```typescript
 * import { createHtmlTransformer, TextConverter } from 'pkg/lark/html-converter'
 * 
 * const transformer = createHtmlTransformer()
 * 
 * // 注册自定义转换器
 * transformer.registerConverter('CUSTOM_TYPE', new CustomConverter())
 * 
 * const result = transformer.transform(docx.rootBlock)
 * ```
 */
export function createHtmlTransformer(): HtmlTransformer {
  return new HtmlTransformer()
} 