# 飞书文档 HTML 转换器

本模块提供将飞书文档块结构转换为 HTML 的功能，支持多种块类型和丰富的样式选项。

## 功能特性

### ✅ 已实现的块类型支持

- **文本块** (`TEXT`, `HEADING7-9`) - 普通段落和小标题
- **标题块** (`HEADING1-6`) - 1-6 级标题
- **分割线** (`DIVIDER`) - 水平分割线
- **代码块** (`CODE`) - 语法高亮的代码块
- **引用块** (`QUOTE_CONTAINER`, `CALLOUT`) - 引用和提示框
- **列表** (`BULLET`, `ORDERED`, `TODO`) - 无序列表、有序列表、待办事项
- **图片** (`IMAGE`) - 支持转换为 data URL，确保离线显示
- **表格** (`TABLE`, `CELL`) - 完整的表格结构
- **文件** (`FILE`) - 文件下载链接
- **白板** (`WHITEBOARD`) - 白板内容转换为图片
- **内嵌框架** (`IFRAME`) - 嵌入式内容

### 🎨 样式支持

- **内联样式** - 默认模式，所有样式直接写入 HTML
- **CSS 类名** - 生成独立的 CSS 文件
- **自定义样式** - 支持自定义 CSS 样式覆盖
- **文本格式** - 粗体、斜体、删除线、下划线、链接
- **颜色支持** - 文字颜色、背景色、高亮背景色
- **响应式设计** - 移动端友好的布局

### 🖼️ 图片处理

- **data URL 转换** - 将图片转换为 base64 编码，支持离线查看
- **异步处理** - 不阻塞 HTML 生成，支持批量处理
- **缓存机制** - 避免重复处理相同图片
- **白板支持** - 白板内容自动转换为图片格式
- **尺寸保持** - 保持原始图片尺寸和比例

## 使用方法

### 基本用法

```typescript
import { convertDocxToHtml } from "pkg/lark/html-converter";
import { docx } from "pkg/lark/docx";

// 基本转换（图片异步处理）
const result = convertDocxToHtml(docx.rootBlock, {
  useInlineStyles: true,
  cssClassPrefix: "feishu",
});

console.error(result.html);
console.error(result.images); // 图片元素数组
console.error(result.files); // 文件元素数组
```

### 等待图片处理完成

```typescript
import { convertDocxToHtmlWithImages } from "pkg/lark/html-converter";
import { docx } from "pkg/lark/docx";

// 转换并等待所有图片处理完成
const result = await convertDocxToHtmlWithImages(docx.rootBlock, {
  useInlineStyles: true,
  cssClassPrefix: "feishu",
});

console.error(result.html); // 图片已转换为data URL
```

### 自定义样式

```typescript
const result = convertDocxToHtml(docx.rootBlock, {
  useInlineStyles: false, // 使用CSS类名
  cssClassPrefix: "my-doc",
  customStyles: {
    text: 'font-family: "Microsoft YaHei"; line-height: 1.8;',
    "heading-1": "color: #2c3e50; border-bottom: 2px solid #3498db;",
  },
});

console.error(result.styles); // 生成的CSS样式
```

### 高级用法

```typescript
import { createHtmlTransformer, ImageConverter } from "pkg/lark/html-converter";

// 创建自定义转换器
const transformer = createHtmlTransformer();

// 预处理图片
await ImageConverter.processAllImages(["token1", "token2"], blockMap);

// 转换文档
const result = transformer.transform(docx.rootBlock);
```

## API 参考

### convertDocxToHtml(rootBlock, options?)

将飞书文档根块转换为 HTML。

**参数:**

- `rootBlock: PageBlock | null` - 飞书文档根块
- `options?: HtmlTransformerOptions` - 转换选项

**返回:** `HtmlTransformResult`

### convertDocxToHtmlWithImages(rootBlock, options?)

转换 HTML 并等待所有图片处理完成。

**参数:**

- `rootBlock: PageBlock | null` - 飞书文档根块
- `options?: HtmlTransformerOptions` - 转换选项

**返回:** `Promise<HtmlTransformResult>`

### HtmlTransformerOptions

```typescript
interface HtmlTransformerOptions {
  useInlineStyles?: boolean; // 是否使用内联样式，默认true
  cssClassPrefix?: string; // CSS类名前缀，默认'feishu'
  customStyles?: Record<string, string>; // 自定义样式
  convertImages?: boolean; // 是否转换图片，默认true
  convertFiles?: boolean; // 是否转换文件，默认true
}
```

### HtmlTransformResult

```typescript
interface HtmlTransformResult {
  html: string; // 生成的HTML内容
  images: HTMLImageElement[]; // 图片元素数组
  files: HTMLAnchorElement[]; // 文件元素数组
  styles?: string; // CSS样式（当useInlineStyles为false时）
}
```

## 导出集成

本 HTML 转换器已集成到以下导出功能中：

- **PDF 导出** - 使用内联样式，图片转换为 data URL
- **图片导出** - 支持 html2canvas 渲染
- **Word 导出** - 通过 HTML 中间格式转换

## 注意事项

1. **图片处理** - 图片转换为 data URL 是异步过程，如需同步结果请使用`convertDocxToHtmlWithImages`
2. **样式兼容** - 内联样式模式兼容性最好，适合导出和打印
3. **性能考虑** - 大量图片时建议使用缓存机制避免重复处理
4. **白板支持** - 白板内容会转换为图片，需要相应的权限获取白板数据

## 扩展开发

### 自定义转换器

```typescript
import { BlockConverter, ConversionContext } from "pkg/lark/html-converter";

class CustomConverter extends BlockConverter {
  canHandle(blockType: BlockType): boolean {
    return blockType === "CUSTOM_TYPE";
  }

  convert(block: Blocks, context: ConversionContext): string {
    return '<div class="custom">Custom Content</div>';
  }
}

// 注册自定义转换器
const transformer = createHtmlTransformer();
transformer.registerConverter("CUSTOM_TYPE", new CustomConverter());
```

### 样式主题

```typescript
const darkTheme = {
  text: "color: #e8e8e8; background: #2d2d2d;",
  "heading-1": "color: #ffffff; border-bottom: 2px solid #4a9eff;",
  code: "background: #1e1e1e; color: #d4d4d4; border: 1px solid #404040;",
};

const result = convertDocxToHtml(docx.rootBlock, {
  useInlineStyles: false,
  customStyles: darkTheme,
});
```
