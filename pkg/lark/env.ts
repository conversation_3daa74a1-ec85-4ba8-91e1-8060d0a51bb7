// eslint-disable-next-line @typescript-eslint/no-empty-function
const noop = () => { }

/**
 * Toast 提示框配置选项接口
 */
interface ToastOptions {
  /** 提示框的唯一标识 */
  key?: string
  /** 提示框显示的内容 */
  content: string
  /** 操作按钮的文本 */
  actionText?: string
  /** 提示框显示时长(毫秒) */
  duration?: number
  /** 是否保持显示 */
  keepAlive?: boolean
  /** 是否显示关闭按钮 */
  closable?: boolean
  /** 操作按钮点击回调 */
  onActionClick?: () => void
  /** 提示框关闭回调 */
  onClose?: () => void
}

export interface Toast {
  error: (options: ToastOptions) => void
  warning: (options: ToastOptions) => void
  info: (options: ToastOptions) => void
  loading: (options: ToastOptions) => void
  success: (options: ToastOptions) => void
  remove: (key: string) => void
}

const defaultToast: Toast = {
  error: noop,
  warning: noop,
  info: noop,
  loading: noop,
  success: noop,
  remove: noop,
}

export const Toast: Toast = window.Toast ?? defaultToast

export interface User {
  language: string
}

export const User: User | undefined = window.User

export interface PageMain {
  blockManager: {
    /**
     * @deprecated
     */
    model?: {
      rootBlockModel: import('./docx').PageBlock
    }
    rootBlockModel: import('./docx').PageBlock
  }

  locateBlockWithRecordIdImpl(
    recordId: string,
    options?: Record<string, unknown>,
  ): Promise<boolean>
}

export const PageMain: PageMain | undefined = window.PageMain
