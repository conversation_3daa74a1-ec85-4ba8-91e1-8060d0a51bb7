import { PageBlock, Blocks, BlockType } from '../docx'
import { imageCache } from './image-cache'

/**
 * 预处理所有图片，将它们转换为data URL
 */
export async function preprocessImages(rootBlock: PageBlock): Promise<void> {
  console.error('🖼️ 开始预处理图片...')

  // 收集所有图片块
  const imageTokens: string[] = []
  const imageBlocks = new Map<string, Blocks>()

  collectImageBlocks(rootBlock, imageTokens, imageBlocks)

  console.error(`📋 收集到 ${imageTokens.length} 张图片`)

  if (imageTokens.length === 0) {
    console.error('ℹ️ 没有发现图片，跳过预处理')
    return
  }

  // 检查缓存，过滤已处理的图片
  const uncachedTokens = imageTokens.filter(token => !imageCache.has(token))
  console.error(`📊 缓存状态: ${imageTokens.length - uncachedTokens.length}/${imageTokens.length} 张图片已缓存`)

  if (uncachedTokens.length === 0) {
    console.error('✅ 所有图片都已缓存，跳过处理')
    return
  }

  // 处理未缓存的图片
  console.error(`⚡ 开始处理 ${uncachedTokens.length} 张未缓存的图片...`)
  const results = await processAllImages(uncachedTokens, imageBlocks)
  console.error(`✅ 图片预处理完成，新处理 ${results.size}/${uncachedTokens.length} 张图片`)
}

/**
 * 递归收集所有图片块（去重处理）
 */
function collectImageBlocks(block: PageBlock | Blocks, tokens: string[], blocks: Map<string, Blocks>): void {
  if (block.type === BlockType.IMAGE) {
    const imageData = (block.snapshot as any)?.image
    if (imageData?.token) {
      // 只有当token不存在时才添加，避免重复
      if (!blocks.has(imageData.token)) {
        tokens.push(imageData.token)
        blocks.set(imageData.token, block)
      }
    }
  }

  // 递归处理子块
  if (block.children && block.children.length > 0) {
    for (const child of block.children) {
      collectImageBlocks(child, tokens, blocks)
    }
  }
}

/**
 * 处理所有图片（参考旧实现的静态方法）
 */
export async function processAllImages(tokens: string[], blocks: Map<string, Blocks>): Promise<Map<string, string>> {
  const results = new Map<string, string>()

  console.error(`开始处理 ${tokens.length} 张图片...`)

  // 并行处理所有图片
  const promises = tokens.map(async (token, index) => {
    try {
      const block = blocks.get(token)
      if (!block || block.type !== BlockType.IMAGE) return

      const dataUrl = await processImageBlock(block)
      if (dataUrl) {
        results.set(token, dataUrl)
        imageCache.set(token, dataUrl)
        console.error(`图片 ${index + 1}/${tokens.length} 处理完成: ${(block.snapshot as any)?.image?.name || 'unknown'}`)
      }
    } catch (error) {
      console.error(`图片 ${index + 1}/${tokens.length} 处理失败:`, error)
    }
  })

  await Promise.all(promises)
  console.error(`所有图片处理完成，成功处理 ${results.size}/${tokens.length} 张图片`)

  return results
}

/**
 * 处理单个图片块
 */
async function processImageBlock(block: Blocks): Promise<string | null> {
  try {
    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    // 获取图片源
    const sources = await fetchImageSources(block)
    if (!sources?.src) {
      console.error('❌ 无法获取图片源URL:', imageData.name || 'unknown')
      return null
    }

    // 下载并转换为data URL
    const response = await fetch(sources.src)
    if (!response.ok) {
      console.error('❌ 图片下载失败:', response.status, response.statusText)
      return null
    }

    const blob = await response.blob()
    const dataUrl = await blobToDataUrl(blob)
    
    // 只输出关键信息
    console.error(`✅ 图片转换完成: ${imageData.name || 'unknown'} (${blob.size} bytes → ${dataUrl.length} chars)`)

    return dataUrl
  } catch (error) {
    console.error('❌ 处理图片失败:', error)
    return null
  }
}

/**
 * 获取图片源
 */
async function fetchImageSources(block: Blocks): Promise<{ src: string; originSrc: string } | null> {
  if (block.type !== BlockType.IMAGE) return null

  try {
    const imageManager = (block as any).imageManager
    if (!imageManager?.fetch) return null

    const imageData = (block.snapshot as any)?.image
    if (!imageData?.token) return null

    // 使用imageManager获取图片源
    const sources = await imageManager.fetch(
      { token: imageData.token, isHD: false },
      {},
      (sources: { originSrc: string; src: string }) => sources
    )

    return sources
  } catch (error) {
    console.error('获取图片源失败:', error)
    return null
  }
}

/**
 * 将Blob转换为data URL
 */
function blobToDataUrl(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = reject
    reader.readAsDataURL(blob)
  })
} 