import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'

/**
 * 转换文件块
 */
export function transformFile(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.FILE) return ''

  const fileData = block.snapshot?.file
  if (!fileData) return ''

  const { name, token } = fileData

  // 如果启用了文件转换，添加到上下文中供后续处理
  if (context.options.convertFiles !== false) {
    const linkElement = document.createElement('a')
    linkElement.textContent = name
    linkElement.dataset.token = token
    linkElement.dataset.name = name
    if ((block as any).record?.id) {
      linkElement.dataset.recordId = (block as any).record.id
    }
    context.files.push(linkElement)
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('file', context, inlineStyles.file)

  // 根据文件扩展名确定图标
  const fileIcon = getFileIcon(name)
  const fileSize = getFileSizeDisplay(fileData)

  if (context.options.useInlineStyles) {
    const defaultStyle = 'display: inline-flex; align-items: center; padding: 8px 12px; border: 1px solid #d1d9e0; border-radius: 6px; background: #f6f8fa; text-decoration: none; color: #0969da; margin: 8px 0; transition: background-color 0.2s;'
    const iconStyle = 'width: 16px; height: 16px; margin-right: 8px;'
    const style = styleConfig.inlineStyle || defaultStyle

    return `<a href="#" data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}" data-record-id="${escapeHtml((block as any).record?.id || '')}" style="${style}">` +
      `<span style="${iconStyle}">${fileIcon}</span>` +
      `<span>${escapeHtml(name)}${fileSize ? ` (${fileSize})` : ''}</span>` +
      `</a>`
  } else {
    return `<a href="#" class="${styleConfig.className}" data-token="${escapeHtml(token)}" data-name="${escapeHtml(name)}" data-record-id="${escapeHtml((block as any).record?.id || '')}">` +
      `<span class="file-icon">${fileIcon}</span>` +
      `<span class="file-name">${escapeHtml(name)}${fileSize ? ` (${fileSize})` : ''}</span>` +
      `</a>`
  }
}

/**
 * 根据文件扩展名获取图标
 */
function getFileIcon(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  // 根据文件扩展名返回对应的图标（使用Unicode字符）
  switch (extension) {
    case 'pdf':
      return '📄'
    case 'doc':
    case 'docx':
      return '📝'
    case 'xls':
    case 'xlsx':
      return '📊'
    case 'ppt':
    case 'pptx':
      return '📑'
    case 'zip':
    case 'rar':
    case '7z':
      return '🗜️'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
      return '🖼️'
    case 'mp3':
    case 'wav':
    case 'flac':
      return '🎵'
    case 'mp4':
    case 'avi':
    case 'mov':
      return '🎬'
    case 'txt':
      return '📄'
    default:
      return '📎'
  }
}

/**
 * 获取文件大小显示
 */
function getFileSizeDisplay(fileData: any): string {
  const size = fileData.size
  if (!size || size === 0) return ''
  
  const units = ['B', 'KB', 'MB', 'GB']
  let fileSize = size
  let unitIndex = 0
  
  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024
    unitIndex++
  }
  
  return `${fileSize.toFixed(1)} ${units[unitIndex]}`
}

/**
 * @deprecated 保留向后兼容性
 */
function formatFileSize(bytes?: number): string {
  return getFileSizeDisplay({ size: bytes })
} 