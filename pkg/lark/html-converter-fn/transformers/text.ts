import { Blocks } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'
import { convertOperationsToHtml } from '../operations'

/**
 * 转换文本块
 */
export function transformText(block: Blocks, context: TransformContext): string {
  const ops = block.zoneState?.content.ops || []
  const content = convertOperationsToHtml(ops, context)

  if (!content.trim()) {
    return '<div></div>'
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('text', context, inlineStyles.text)

  return applyStyle('div', content, styleConfig, context)
} 