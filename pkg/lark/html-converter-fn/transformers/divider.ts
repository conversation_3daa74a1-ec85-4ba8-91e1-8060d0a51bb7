import { Blocks } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'

/**
 * 转换分割线块
 */
export function transformDivider(block: Blocks, context: TransformContext): string {
  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('divider', context, inlineStyles.divider)

  return applyStyle('hr', '', styleConfig, context)
} 