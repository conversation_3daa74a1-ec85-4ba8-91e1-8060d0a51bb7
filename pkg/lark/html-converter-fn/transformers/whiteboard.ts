import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'

/**
 * 转换白板块
 */
export function transformWhiteboard(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.WHITEBOARD) return ''

  const whiteboardData = (block.snapshot as any)?.whiteboard
  if (!whiteboardData) return ''

  const { token, name } = whiteboardData
  const alt = name || '白板'

  // 创建白板容器
  const imgTag = `<img src="" alt="${escapeHtml(alt)}" data-token="${escapeHtml(token)}" data-type="whiteboard">`
  const content = `<div class="whiteboard-content">${imgTag}</div>`

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('whiteboard', context, inlineStyles.whiteboard)

  return applyStyle('div', content, styleConfig, context, ` data-type="whiteboard" data-token="${escapeHtml(token)}"`)
} 