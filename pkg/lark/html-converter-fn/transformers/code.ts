import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'

/**
 * 转换代码块
 */
export function transformCode(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.CODE) return ''

  const language = (block as any).language || ''
  const code = block.zoneState?.allText || ''

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('code', context, inlineStyles.code)

  if (context.options.useInlineStyles) {
    const defaultStyle = 'background: #f6f8fa; border: 1px solid #d1d9e0; border-radius: 6px; padding: 16px; margin: 16px 0; overflow: auto; font-family: Consolas, "Liberation Mono", Menlo, Courier, monospace; font-size: 14px; line-height: 1.45;'
    const style = styleConfig.inlineStyle || defaultStyle

    if (language) {
      return `<pre style="${style}"><code class="language-${escapeHtml(language)}">${escapeHtml(code)}</code></pre>`
    } else {
      return `<pre style="${style}"><code>${escapeHtml(code)}</code></pre>`
    }
  } else {
    if (language) {
      return `<pre class="${styleConfig.className}"><code class="language-${escapeHtml(language)}">${escapeHtml(code)}</code></pre>`
    } else {
      return `<pre class="${styleConfig.className}"><code>${escapeHtml(code)}</code></pre>`
    }
  }
} 