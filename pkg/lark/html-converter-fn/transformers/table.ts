import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'
import { convertOperationsToHtml } from '../operations'
import { transformBlock } from '../transform'

/**
 * 转换表格块
 */
export function transformTable(block: Blocks, context: TransformContext): string {
  if (block.type === BlockType.TABLE) {
    return transformTableBlock(block, context)
  } else if (block.type === BlockType.CELL) {
    return transformCellBlock(block, context)
  }
  
  return ''
}

/**
 * 转换表格块
 */
function transformTableBlock(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.TABLE) return ''

  const { columns_id, rows_id } = (block.snapshot as any) || {}
  const columnCount = columns_id?.length || 0
  const rowCount = rows_id?.length || 0

  if (columnCount === 0 || rowCount === 0) {
    return '<table><tr><td>空表格</td></tr></table>'
  }

  // 将子块按行分组
  const cellBlocks = block.children.filter(child => child.type === BlockType.CELL)
  const rows: Blocks[][] = []

  for (let i = 0; i < rowCount; i++) {
    const rowCells = cellBlocks.slice(i * columnCount, (i + 1) * columnCount)
    rows.push(rowCells)
  }

  const tableRows = rows.map((rowCells, rowIndex) => {
    const cells = rowCells.map(cell => transformCellBlock(cell, context)).join('')
    return `<tr>${cells}</tr>`
  }).join('\n')

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('table', context, inlineStyles.table)

  if (context.options.useInlineStyles) {
    const defaultStyle = 'border-collapse: collapse; width: 100%; margin: 16px 0; border: 1px solid #ddd;'
    const style = styleConfig.inlineStyle || defaultStyle
    return `<table style="${style}">\n${tableRows}\n</table>`
  } else {
    return `<table class="${styleConfig.className}">\n${tableRows}\n</table>`
  }
}

/**
 * 转换单元格块
 */
function transformCellBlock(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.CELL) return ''

  // 处理单元格内的子块
  const childrenHtml = processChildren(block.children || [], context)

  // 如果没有子块内容，尝试从当前块提取文本
  if (childrenHtml.length === 0) {
    const ops = block.zoneState?.content?.ops || []
    const content = convertOperationsToHtml(ops, context)
    if (content.trim()) {
      childrenHtml.push(content)
    }
  }

  const content = childrenHtml.join('') || '&nbsp;'

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('table-cell', context, inlineStyles['table-cell'])

  if (context.options.useInlineStyles) {
    const defaultStyle = 'border: 1px solid #ddd; padding: 8px; vertical-align: top;'
    const style = styleConfig.inlineStyle || defaultStyle
    return `<td style="${style}">${content}</td>`
  } else {
    return `<td class="${styleConfig.className}">${content}</td>`
  }
}

/**
 * 处理表格单元格内的子块，支持列表分组和任意类型的子块
 */
function processChildren(children: Blocks[], context: TransformContext): string[] {
  if (!children || children.length === 0) {
    return []
  }

  const childrenHtml: string[] = []
  const groupedChildren = groupConsecutiveListBlocks(children)
  
  for (const group of groupedChildren) {
    if (group.type === 'list' && group.listType) {
      // 处理列表组 - 表格中的列表使用紧凑样式
      const listHtml = convertTableListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        childrenHtml.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const childHtml = transformBlock(child, context)
        if (childHtml && childHtml.trim()) {
          // 为表格单元格内容添加适当的包装
          const wrappedHtml = wrapCellContent(childHtml, child.type)
          childrenHtml.push(wrappedHtml)
        }
      }
    }
  }

  return childrenHtml
}

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET || 
                       block.type === BlockType.ORDERED || 
                       block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换表格中的列表组（使用紧凑样式）
 */
function convertTableListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformBlock(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  // 表格中的列表使用更紧凑的样式
  let listStyle = ''
  const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'

  if (context.options.useInlineStyles) {
    switch (listType) {
      case BlockType.TODO:
        listStyle = 'list-style: none; padding-left: 0; margin: 4px 0;'
        break
      case BlockType.ORDERED:
      case BlockType.BULLET:
        listStyle = 'margin: 4px 0; padding-left: 16px;'
        break
      default:
        listStyle = 'margin: 4px 0; padding-left: 16px;'
    }
  }

  // 为有序列表和无序列表添加标记颜色样式
  if (context.options.useInlineStyles && (listType === BlockType.ORDERED || listType === BlockType.BULLET)) {
    const markerStyle = 'li::marker { color: #0084ff; }'
    return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
  }

  return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
}

/**
 * 为表格单元格内容添加适当的包装
 */
function wrapCellContent(content: string, blockType: BlockType): string {
  // 文本类型现在使用div，不需要特殊处理margin
  if (blockType === BlockType.TEXT ||
      blockType === BlockType.HEADING7 ||
      blockType === BlockType.HEADING8 ||
      blockType === BlockType.HEADING9) {
    return content
  }

  // 标题类型保持原样
  if (blockType === BlockType.HEADING1 ||
      blockType === BlockType.HEADING2 ||
      blockType === BlockType.HEADING3 ||
      blockType === BlockType.HEADING4 ||
      blockType === BlockType.HEADING5 ||
      blockType === BlockType.HEADING6) {
    return content
  }

  // 其他类型直接返回
  return content
} 