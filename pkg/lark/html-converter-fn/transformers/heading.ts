import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'
import { convertOperationsToHtml } from '../operations'

/**
 * 转换标题块
 */
export function transformHeading(block: Blocks, context: TransformContext): string {
  const level = getHeadingLevel(block.type)
  const ops = block.zoneState?.content.ops || []
  let content = convertOperationsToHtml(ops, context)

  // 处理自动编号
  if ('snapshot' in block && block.snapshot && 'seq' in block.snapshot) {
    const snapshot = block.snapshot as any
    if (typeof snapshot.seq === 'string') {
      const sequenceNumber = generateSequenceNumber(snapshot, block.type)
      if (sequenceNumber) {
        content = `${sequenceNumber} ${content}`
      }
    }
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig(`heading-${level}`, context, inlineStyles[`heading-${level}`])

  return applyStyle(`h${level}`, content, styleConfig, context)
}

function getHeadingLevel(blockType: BlockType): number {
  switch (blockType) {
    case BlockType.HEADING1: return 1
    case BlockType.HEADING2: return 2
    case BlockType.HEADING3: return 3
    case BlockType.HEADING4: return 4
    case BlockType.HEADING5: return 5
    case BlockType.HEADING6: return 6
    default: return 1
  }
}

function generateSequenceNumber(snapshot: any, blockType: BlockType): string {
  if (snapshot.seq === 'auto') {
    return ''
  }

  if (snapshot.seq_level === 'auto') {
    return `${snapshot.seq}.`
  }

  return `${snapshot.seq}.`
} 