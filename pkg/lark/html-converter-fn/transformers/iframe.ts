import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'

/**
 * 转换内嵌框架块
 */
export function transformIframe(block: Blocks, context: TransformContext): string {
  if (block.type !== BlockType.IFRAME) return ''

  const iframeData = (block.snapshot as any)?.iframe
  if (!iframeData) return ''

  const { url, title, width, height } = iframeData.component || iframeData
  const safeUrl = escapeHtml(url)
  const safeTitle = escapeHtml(title || 'Embedded Content')

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('iframe', context, inlineStyles.iframe)

  const sizeAttrs = buildSizeAttributes(width, height)
  const attrs = ` src="${safeUrl}" title="${safeTitle}" frameborder="0" allowfullscreen${sizeAttrs}`

  if (context.options.useInlineStyles && styleConfig.inlineStyle) {
    return `<iframe${attrs} style="${styleConfig.inlineStyle}"></iframe>`
  } else {
    return `<iframe${attrs} class="${styleConfig.className}"></iframe>`
  }
}

function buildSizeAttributes(width?: number, height?: number): string {
  const attrs: string[] = []
  
  if (width) {
    attrs.push(` width="${width}"`)
  }
  
  if (height) {
    attrs.push(` height="${height}"`)
  }
  
  return attrs.join('')
} 