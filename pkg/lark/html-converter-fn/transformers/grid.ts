import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, escapeHtml } from '../context'
import { getInlineStyles } from '../styles'
import { transformBlock } from '../transform'

/**
 * 转换网格布局块
 */
export function transformGrid(block: Blocks, context: TransformContext): string {
  if (block.type === BlockType.GRID) {
    return transformGridBlock(block, context)
  } else if (block.type === BlockType.GRID_COLUMN) {
    return transformGridColumnBlock(block, context)
  }
  
  return ''
}

/**
 * 转换网格块
 */
function transformGridBlock(block: Blocks, context: TransformContext): string {
  // 收集所有子列
  const columns: string[] = []

  if ('children' in block && block.children) {
    for (const child of block.children) {
      if (child.type === BlockType.GRID_COLUMN) {
        const columnHtml = transformGridColumnBlock(child, context)
        if (columnHtml) {
          columns.push(columnHtml)
        }
      }
    }
  }

  if (columns.length === 0) {
    return ''
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('grid', context, inlineStyles.grid)

  if (context.options.useInlineStyles) {
    const defaultStyle = 'display: flex; flex-wrap: wrap; gap: 16px; margin: 16px 0; align-items: flex-start;'
    const style = styleConfig.inlineStyle || defaultStyle

    return `<div class="grid-container" style="${style}">${columns.join('')}</div>`
  } else {
    return `<div class="${styleConfig.className}">${columns.join('')}</div>`
  }
}

/**
 * 转换网格列块
 */
function transformGridColumnBlock(block: Blocks, context: TransformContext): string {
  // 转换子块
  const childrenHtml = convertChildBlocks(block.children || [], context)

  if (childrenHtml.length === 0) {
    return ''
  }

  const inlineStyles = getInlineStyles()
  const styleConfig = getStyleConfig('grid-column', context, inlineStyles['grid-column'])

  if (context.options.useInlineStyles) {
    const defaultStyle = 'flex: 1; min-width: 0; box-sizing: border-box;'
    const style = styleConfig.inlineStyle || defaultStyle

    return `<div class="grid-column" style="${style}">${childrenHtml.join('')}</div>`
  } else {
    return `<div class="${styleConfig.className}">${childrenHtml.join('')}</div>`
  }
}

/**
 * 转换子块
 */
function convertChildBlocks(children: Blocks[], context: TransformContext): string[] {
  const childrenHtml: string[] = []
  
  for (const child of children) {
    const childHtml = transformBlock(child, context)
    if (childHtml && childHtml.trim()) {
      childrenHtml.push(childHtml)
    }
  }
  
  return childrenHtml
} 