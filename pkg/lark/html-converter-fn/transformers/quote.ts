import { Blocks, BlockType } from '../../docx'
import { TransformContext, getStyleConfig, applyStyle } from '../context'
import { getInlineStyles } from '../styles'
import { transformBlock } from '../transform'

/**
 * 对连续的列表块进行分组
 */
function groupConsecutiveListBlocks(blocks: Blocks[]): Array<{
  type: 'list' | 'other'
  listType?: BlockType
  blocks: Blocks[]
}> {
  const groups: Array<{
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  }> = []

  let currentGroup: {
    type: 'list' | 'other'
    listType?: BlockType
    blocks: Blocks[]
  } | null = null

  for (const block of blocks) {
    const isListBlock = block.type === BlockType.BULLET || 
                       block.type === BlockType.ORDERED || 
                       block.type === BlockType.TODO

    if (isListBlock) {
      if (!currentGroup || currentGroup.type !== 'list' || currentGroup.listType !== block.type) {
        // 开始新的列表组
        currentGroup = {
          type: 'list',
          listType: block.type,
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前列表组
        currentGroup.blocks.push(block)
      }
    } else {
      if (!currentGroup || currentGroup.type !== 'other') {
        // 开始新的非列表组
        currentGroup = {
          type: 'other',
          blocks: [block]
        }
        groups.push(currentGroup)
      } else {
        // 添加到当前非列表组
        currentGroup.blocks.push(block)
      }
    }
  }

  return groups
}

/**
 * 转换引用块内的列表组
 */
function convertQuoteListGroup(listBlocks: Blocks[], listType: BlockType, context: TransformContext): string {
  if (!listBlocks.length) return ''

  const listItems = listBlocks.map(block => transformBlock(block, context)).filter(Boolean)
  if (!listItems.length) return ''

  const listTag = listType === BlockType.ORDERED ? 'ol' : 'ul'
  
  if (context.options.useInlineStyles) {
    let listStyle = ''
    switch (listType) {
      case BlockType.TODO:
        listStyle = 'list-style: none; padding-left: 0; margin: 8px 0;'
        break
      case BlockType.ORDERED:
      case BlockType.BULLET:
        listStyle = 'padding-left: 20px; margin: 8px 0;'
        break
      default:
        listStyle = 'padding-left: 20px; margin: 8px 0;'
    }

    // 为有序列表和无序列表添加标记颜色样式
    if (listType === BlockType.ORDERED || listType === BlockType.BULLET) {
      const markerStyle = 'li::marker { color: #0084ff; }'
      return `<style>${markerStyle}</style><${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    } else {
      return `<${listTag} style="${listStyle}">${listItems.join('')}</${listTag}>`
    }
  } else {
    const className = `${context.options.cssClassPrefix || 'feishu'}-${listType === BlockType.TODO ? 'todo-list' : listType === BlockType.ORDERED ? 'ordered-list' : 'bullet-list'}`
    return `<${listTag} class="${className}">${listItems.join('')}</${listTag}>`
  }
}

/**
 * 转换子块列表（支持列表分组）
 */
function transformChildrenWithListGrouping(children: Blocks[], context: TransformContext): string[] {
  if (!children || children.length === 0) {
    return []
  }

  const childrenHtml: string[] = []
  const groupedChildren = groupConsecutiveListBlocks(children)
  
  for (const group of groupedChildren) {
    if (group.type === 'list' && group.listType) {
      // 处理列表组
      const listHtml = convertQuoteListGroup(group.blocks, group.listType, context)
      if (listHtml) {
        childrenHtml.push(listHtml)
      }
    } else {
      // 处理单个非列表块
      for (const child of group.blocks) {
        const childHtml = transformBlock(child, context)
        if (childHtml && childHtml.trim()) {
          childrenHtml.push(childHtml)
        }
      }
    }
  }

  return childrenHtml
}

/**
 * 转换引用块
 */
export function transformQuote(block: Blocks, context: TransformContext): string {
  const children = block.children || []
  const childrenHtml = transformChildrenWithListGrouping(children, context)
  const content = childrenHtml.join('\n')

  const inlineStyles = getInlineStyles()
  const isCallout = block.type === BlockType.CALLOUT
  const styleType = isCallout ? 'callout' : 'quote'
  const styleConfig = getStyleConfig(styleType, context, inlineStyles[styleType])

  const tag = isCallout ? 'div' : 'blockquote'
  return applyStyle(tag, content, styleConfig, context)
} 