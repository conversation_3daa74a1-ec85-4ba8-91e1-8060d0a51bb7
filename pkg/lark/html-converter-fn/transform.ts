import { Blocks, BlockType } from '../docx'
import { TransformContext } from './context'
import {
  transformText,
  transformHeading,
  transformDivider,
  transformCode,
  transformQuote,
  transformList,
  transformImage,
  transformTable,
  transformFile,
  transformWhiteboard,
  transformIframe,
  transformGrid
} from './transformers'

// 转换器映射
const transformers: Partial<Record<BlockType, (block: Blocks, context: TransformContext) => string>> = {
  [BlockType.TEXT]: transformText,
  [BlockType.HEADING1]: transformHeading,
  [BlockType.HEADING2]: transformHeading,
  [BlockType.HEADING3]: transformHeading,
  [BlockType.HEADING4]: transformHeading,
  [BlockType.HEADING5]: transformHeading,
  [BlockType.HEADING6]: transformHeading,
  [BlockType.HEADING7]: transformText, // 作为普通文本处理
  [BlockType.HEADING8]: transformText,
  [BlockType.HEADING9]: transformText,
  [BlockType.DIVIDER]: transformDivider,
  [BlockType.CODE]: transformCode,
  [BlockType.QUOTE_CONTAINER]: transformQuote,
  [BlockType.CALLOUT]: transformQuote,
  [BlockType.BULLET]: transformList,
  [BlockType.ORDERED]: transformList,
  [BlockType.TODO]: transformList,
  [BlockType.IMAGE]: transformImage,
  [BlockType.TABLE]: transformTable,
  [BlockType.CELL]: transformTable,
  [BlockType.FILE]: transformFile,
  [BlockType.WHITEBOARD]: transformWhiteboard,
  [BlockType.IFRAME]: transformIframe,
  [BlockType.GRID]: transformGrid,
  [BlockType.GRID_COLUMN]: transformGrid
}

/**
 * 转换单个块
 */
export function transformBlock(block: Blocks, context: TransformContext): string {
  const transformer = transformers[block.type]
  
  if (transformer) {
    try {
      return transformer(block, context)
    } catch (error) {
      console.error(`转换块失败 (${block.type}):`, error)
      return createFallbackHtml(block, context)
    }
  }

  // 未知类型的降级处理
  return createFallbackHtml(block, context)
}

/**
 * 转换子块列表
 */
export function transformChildren(children: Blocks[], context: TransformContext): string[] {
  return children
    .map(child => transformBlock(child, context))
    .filter(html => html.trim())
}

/**
 * 创建降级HTML
 */
function createFallbackHtml(block: Blocks, context: TransformContext): string {
  const textContent = extractTextContent(block)
  
  if (!textContent.trim()) {
    return ''
  }

  return `<div class="${context.options.cssClassPrefix}-unknown" data-type="${block.type}">${textContent}</div>`
}

/**
 * 提取块的文本内容
 */
function extractTextContent(block: Blocks): string {
  // 尝试从zoneState中提取文本
  if (block.zoneState?.content?.ops) {
    return block.zoneState.content.ops
      .map(op => op.insert || '')
      .join('')
  }

  // 递归提取子块的文本内容
  if (block.children && block.children.length > 0) {
    return block.children
      .map(child => extractTextContent(child))
      .join(' ')
  }

  return ''
} 