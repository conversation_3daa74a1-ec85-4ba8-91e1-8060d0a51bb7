import { Operation, Attributes } from '../docx'
import { TransformContext, escapeHtml } from './context'

/**
 * 转换操作列表为HTML
 */
export function convertOperationsToHtml(ops: Operation[] = [], context: TransformContext): string {
  if (!ops.length) return ''

  return ops.map(op => convertOperationToHtml(op, context)).join('')
}

/**
 * 转换单个操作为HTML
 */
export function convertOperationToHtml(op: Operation, context: TransformContext): string {
  const { attributes, insert } = op

  if (!insert) return ''

  // 处理特殊字符转义
  let content = escapeHtml(insert)

  // 处理内联代码
  if (attributes?.inlineCode) {
    return `<code>${content}</code>`
  }

  // 处理数学公式
  if (attributes?.equation) {
    const equation = attributes.equation.replace(/\n$/, '')
    return `<span class="equation">${escapeHtml(equation)}</span>`
  }

  // 应用文本格式
  content = applyTextFormats(content, attributes, context)

  return content
}

/**
 * 应用文本格式
 */
function applyTextFormats(content: string, attributes?: Attributes, context?: TransformContext): string {
  if (!attributes) return content

  let html = content

  // 粗体
  if (attributes.bold) {
    html = `<strong>${html}</strong>`
  }

  // 斜体
  if (attributes.italic) {
    html = `<em>${html}</em>`
  }

  // 删除线
  if (attributes.strikethrough) {
    html = `<del>${html}</del>`
  }

  // 下划线
  if ((attributes as any).underline) {
    html = `<u>${html}</u>`
  }

  // 链接
  if (attributes.link) {
    const url = decodeURIComponent(attributes.link)
    html = `<a href="${escapeHtml(url)}">${html}</a>`
  }

  // 颜色和背景色处理
  const textHighlight = attributes.textHighlightBackground as string
  const textColor = attributes.textHighlight as string
  const htmlAttrs = attributes as any

  if (htmlAttrs.color || htmlAttrs.backgroundColor || textHighlight || textColor || htmlAttrs.fontSize) {
    const styles: string[] = []

    if (htmlAttrs.color) {
      styles.push(`color: ${htmlAttrs.color}`)
    }

    // 支持 textHighlight 属性作为文字颜色
    if (textColor) {
      styles.push(`color: ${textColor}`)
    }

    if (htmlAttrs.backgroundColor) {
      styles.push(`background-color: ${htmlAttrs.backgroundColor}`)
    }

    // 支持 textHighlightBackground 属性
    if (textHighlight) {
      styles.push(`background-color: ${textHighlight}`)
    }

    if (htmlAttrs.fontSize) {
      styles.push(`font-size: ${htmlAttrs.fontSize}`)
    }

    if (styles.length > 0) {
      html = `<span style="${styles.join('; ')}">${html}</span>`
    }
  }

  return html
} 